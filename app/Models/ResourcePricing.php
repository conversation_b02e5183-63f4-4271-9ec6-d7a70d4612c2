<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ResourcePricing extends Model
{
    use HasFactory;

    protected $fillable = [
        'resource_id',
        'resource_type',
        'unit_type',
        'price_per_unit_per_month',
        'effective_date',
        'description',
    ];

    protected $casts = [
        'price_per_unit_per_month' => 'decimal:8',
        'effective_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 多态关联到资源
     */
    public function resource(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 每分钟价格访问器
     */
    public function getPricePerMinuteAttribute(): string
    {
        // 获取当前月份的天数
        $daysInMonth = now()->daysInMonth;
        // 计算当前月份的总分钟数
        $minutesInMonth = $daysInMonth * 24 * 60;

        return bcdiv((string) $this->price_per_unit_per_month, (string) $minutesInMonth, 8);
    }

    /**
     * 计算指定使用量的每分钟价格
     */
    public function calculateCostPerMinute(float $usage): string
    {
        $pricePerMinute = $this->price_per_minute;

        return bcmul($pricePerMinute, (string) $usage, 8);
    }

    /**
     * 每日价格访问器
     */
    public function getPricePerDayAttribute(): string
    {
        $daysInMonth = now()->daysInMonth;

        return bcdiv((string) $this->price_per_unit_per_month, (string) $daysInMonth, 8);
    }

    /**
     * 每小时价格访问器
     */
    public function getPricePerHourAttribute(): string
    {
        $hoursInMonth = now()->daysInMonth * 24;

        return bcdiv((string) $this->price_per_unit_per_month, (string) $hoursInMonth, 8);
    }

    /**
     * 根据资源使用量计算费用
     */
    public function calculateCost(array $usage, int $minutes = 1): array
    {
        $unitUsage = $usage[$this->unit_type] ?? 0;

        if ($unitUsage <= 0) {
            return [
                'usage' => 0,
                'unit_type' => $this->unit_type,
                'cost_per_minute' => config('pricable.default_cost'),
                'total_cost' => config('pricable.default_cost'),
            ];
        }

        $costPerMinute = $this->calculateCostPerMinute($unitUsage);
        $totalCost = bcmul($costPerMinute, (string) $minutes, 8);

        return [
            'usage' => $unitUsage,
            'unit_type' => $this->unit_type,
            'cost_per_minute' => $costPerMinute,
            'total_cost' => $totalCost,
        ];
    }

    /**
     * 范围查询：当前有效的定价
     */
    public function scopeCurrent($query)
    {
        return $query->where('effective_date', '<=', now())
            ->latest('effective_date');
    }

    /**
     * 范围查询：根据资源类型过滤
     */
    public function scopeForResourceType($query, string $resourceType)
    {
        return $query->where('resource_type', $resourceType);
    }

    /**
     * 范围查询：根据单位类型过滤
     */
    public function scopeForUnitType($query, string $unitType)
    {
        return $query->where('unit_type', $unitType);
    }

    /**
     * 范围查询：根据资源 ID 过滤
     */
    public function scopeForResource($query, Model $resource)
    {
        return $query->where('resource_type', get_class($resource))
            ->where('resource_id', $resource->id);
    }

    /**
     * 获取格式化的价格信息
     */
    public function getFormattedPricing(): array
    {
        return [
            'id' => $this->id,
            'unit_type' => $this->unit_type,
            'price_per_month' => (string) $this->price_per_unit_per_month,
            'price_per_minute' => $this->price_per_minute,
            'effective_date' => $this->effective_date->toISOString(),
            'description' => $this->description,
            'resource_type' => $this->resource_type,
            'resource_id' => $this->resource_id,
        ];
    }

    /**
     * 检查定价是否已生效
     */
    public function isEffective(): bool
    {
        return $this->effective_date <= now();
    }

    /**
     * 创建资源定价的静态方法
     */
    public static function createForResource(Model $resource, string $unitType, string $pricePerMonth, ?string $description = null, $effectiveDate = null): self
    {
        return static::create([
            'resource_id' => $resource->id,
            'resource_type' => get_class($resource),
            'unit_type' => $unitType,
            'price_per_unit_per_month' => $pricePerMonth,
            'description' => $description,
            'effective_date' => $effectiveDate ?? now(),
        ]);
    }

    /**
     * 获取指定资源的当前定价
     */
    public static function getCurrentPricingForResource(Model $resource, string $unitType): ?self
    {
        return static::forResource($resource)
            ->forUnitType($unitType)
            ->current()
            ->first();
    }

    /**
     * 批量获取资源的所有定价
     */
    public static function getAllCurrentPricingForResource(Model $resource): array
    {
        $pricings = static::forResource($resource)
            ->current()
            ->get()
            ->groupBy('unit_type');

        $result = [];
        foreach ($pricings as $unitType => $unitPricings) {
            $result[$unitType] = $unitPricings->first();
        }

        return $result;
    }

    /**
     * 获取单位类型的显示名称
     */
    public function getUnitTypeDisplayName(): string
    {
        // 首先获取资源类型
        $resourceType = $this->getResourceTypeFromModelClass($this->resource_type);

        // 从配置中获取单位类型信息
        $unitTypeConfig = config("pricable.unit_types.{$resourceType}.{$this->unit_type}");

        if ($unitTypeConfig && isset($unitTypeConfig['name'])) {
            return $unitTypeConfig['name'];
        }

        // 如果配置中没有，使用备用显示名称
        return 'N/A';
    }

    /**
     * 获取资源类型（从模型类）
     */
    private function getResourceTypeFromModelClass(string $modelClass): string
    {
        $models = config('pricable.models', []);

        return $models[$modelClass] ?? 'unknown';
    }
}
