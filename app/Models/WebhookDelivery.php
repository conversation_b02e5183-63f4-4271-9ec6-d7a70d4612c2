<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WebhookDelivery extends Model
{
    use HasFactory;

    protected $fillable = [
        'webhook_endpoint_id',
        'event_type',
        'resource_name',
        'namespace',
        'cluster_name',
        'cluster_id',
        'payload',
        'status',
        'attempts',
        'response_status',
        'response_body',
        'error_message',
        'delivered_at',
        'failed_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'delivered_at' => 'datetime',
        'failed_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_SUCCESS = 'success';
    public const STATUS_FAILED = 'failed';

    /**
     * 获取所属的 webhook 端点
     */
    public function webhookEndpoint(): BelongsTo
    {
        return $this->belongsTo(WebhookEndpoint::class);
    }

    /**
     * 检查是否成功发送
     */
    public function isSuccessful(): bool
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    /**
     * 检查是否失败
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 检查是否待发送
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 标记为成功
     */
    public function markAsSuccessful(int $responseStatus, ?string $responseBody = null): void
    {
        $this->update([
            'status' => self::STATUS_SUCCESS,
            'response_status' => $responseStatus,
            'response_body' => $responseBody,
            'delivered_at' => now(),
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(string $errorMessage, ?int $responseStatus = null, ?string $responseBody = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
            'response_status' => $responseStatus,
            'response_body' => $responseBody,
            'failed_at' => now(),
        ]);
    }

    /**
     * 增加尝试次数
     */
    public function incrementAttempts(): void
    {
        $this->increment('attempts');
    }
}
