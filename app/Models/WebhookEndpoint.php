<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WebhookEndpoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'name',
        'url',
        'secret',
        'resource_types',
        'event_types',
        'is_active',
        'description',
        'timeout',
        'max_retries',
    ];

    protected $casts = [
        'resource_types' => 'array',
        'event_types' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * 获取所属的工作空间
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * 获取所有的发送记录
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(WebhookDelivery::class);
    }

    /**
     * 获取最近的发送记录
     */
    public function recentDeliveries(): HasMany
    {
        return $this->deliveries()->latest()->limit(10);
    }

    /**
     * 检查是否监听指定的资源类型
     */
    public function listensToResourceType(string $resourceType): bool
    {
        return in_array($resourceType, $this->resource_types ?? []);
    }

    /**
     * 检查是否监听指定的事件类型
     */
    public function listensToEventType(string $eventType): bool
    {
        $configuredTypes = $this->event_types ?? [];

        // 精确匹配
        if (in_array($eventType, $configuredTypes)) {
            return true;
        }

        // 通配符匹配 (如 "deployment.*" 匹配 "deployment.created")
        foreach ($configuredTypes as $configuredType) {
            if (str_ends_with($configuredType, '.*')) {
                $prefix = substr($configuredType, 0, -2);
                if (str_starts_with($eventType, $prefix . '.')) {
                    return true;
                }
            }
        }

        // 动作匹配 (如 "created" 匹配 "deployment.created")
        if (str_contains($eventType, '.')) {
            $action = substr($eventType, strrpos($eventType, '.') + 1);
            if (in_array($action, $configuredTypes)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取可用的资源类型选项
     */
    public static function getAvailableResourceTypes(): array
    {
        return [
            'deployments' => 'Deployment',
            'statefulsets' => 'StatefulSet',
            'services' => 'Service',
            'pods' => 'Pod',
            'ingresses' => 'Ingress',
            'storages' => 'Storage (PVC)',
            'secrets' => 'Secret',
            'configmaps' => 'ConfigMap',
            'horizontalpodautoscalers' => 'HPA',
            'events' => 'Event',
        ];
    }

    /**
     * 获取可用的事件类型选项
     */
    public static function getAvailableEventTypes(): array
    {
        return [
            // 通用事件类型
            'created' => '创建',
            'updated' => '更新',
            'deleted' => '删除',

            // Deployment 特定事件
            'deployment.created' => 'Deployment 创建',
            'deployment.updated' => 'Deployment 更新',
            'deployment.deleted' => 'Deployment 删除',
            'deployment.scaled' => 'Deployment 扩缩容',
            'deployment.rollout_started' => 'Deployment 滚动更新开始',
            'deployment.rollout_completed' => 'Deployment 滚动更新完成',
            'deployment.rollout_failed' => 'Deployment 滚动更新失败',

            // Pod 特定事件
            'pod.created' => 'Pod 创建',
            'pod.started' => 'Pod 启动',
            'pod.ready' => 'Pod 就绪',
            'pod.failed' => 'Pod 失败',
            'pod.succeeded' => 'Pod 成功完成',
            'pod.deleted' => 'Pod 删除',
            'pod.evicted' => 'Pod 被驱逐',
            'pod.oom_killed' => 'Pod 内存不足被杀死',
            'pod.image_pull_failed' => 'Pod 镜像拉取失败',
            'pod.crash_loop_backoff' => 'Pod 崩溃循环',

            // Service 特定事件
            'service.created' => 'Service 创建',
            'service.updated' => 'Service 更新',
            'service.deleted' => 'Service 删除',
            'service.endpoint_ready' => 'Service 端点就绪',
            'service.endpoint_not_ready' => 'Service 端点不可用',

            // Ingress 特定事件
            'ingress.created' => 'Ingress 创建',
            'ingress.updated' => 'Ingress 更新',
            'ingress.deleted' => 'Ingress 删除',
            'ingress.tls_configured' => 'Ingress TLS 配置完成',
            'ingress.backend_error' => 'Ingress 后端服务错误',

            // Storage 特定事件
            'pvc.created' => 'PVC 创建',
            'pvc.bound' => 'PVC 绑定成功',
            'pvc.pending' => 'PVC 等待绑定',
            'pvc.deleted' => 'PVC 删除',
            'pvc.resize_started' => 'PVC 扩容开始',
            'pvc.resize_completed' => 'PVC 扩容完成',

            // Secret & ConfigMap 事件
            'secret.created' => 'Secret 创建',
            'secret.updated' => 'Secret 更新',
            'secret.deleted' => 'Secret 删除',
            'configmap.created' => 'ConfigMap 创建',
            'configmap.updated' => 'ConfigMap 更新',
            'configmap.deleted' => 'ConfigMap 删除',

            // HPA 特定事件
            'hpa.created' => 'HPA 创建',
            'hpa.updated' => 'HPA 更新',
            'hpa.deleted' => 'HPA 删除',
            'hpa.scaled_up' => 'HPA 扩容',
            'hpa.scaled_down' => 'HPA 缩容',
            'hpa.unable_to_scale' => 'HPA 无法扩缩容',

            // 通配符事件类型
            'deployment.*' => 'Deployment 所有事件',
            'pod.*' => 'Pod 所有事件',
            'service.*' => 'Service 所有事件',
            'ingress.*' => 'Ingress 所有事件',
            'pvc.*' => 'PVC 所有事件',
            'secret.*' => 'Secret 所有事件',
            'configmap.*' => 'ConfigMap 所有事件',
            'hpa.*' => 'HPA 所有事件',
        ];
    }
}
