<?php

namespace App\Models;

use App\Constants\WebhookEventTypes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WebhookEndpoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'name',
        'url',
        'secret',
        'event_types',
        'listen_all_events',
        'is_active',
        'description',
        'timeout',
        'max_retries',
    ];

    protected $casts = [
        'event_types' => 'array',
        'listen_all_events' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * 获取所属的工作空间
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * 获取所有的发送记录
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(WebhookDelivery::class);
    }

    /**
     * 获取最近的发送记录
     */
    public function recentDeliveries(): HasMany
    {
        return $this->deliveries()->latest()->limit(10);
    }



    /**
     * 检查是否监听指定的事件类型（优化版本）
     */
    public function listensToEventType(string $eventType): bool
    {
        // 如果监听所有事件，直接返回 true
        if ($this->listen_all_events) {
            return true;
        }

        // 使用常量类进行匹配
        return WebhookEventTypes::eventMatches($eventType, $this->event_types ?? []);
    }



    /**
     * 获取可用的事件类型选项（仅返回事件类型名称）
     */
    public static function getAvailableEventTypes(): array
    {
        return WebhookEventTypes::getAllEventTypes();
    }

    /**
     * 获取按资源分组的事件类型
     */
    public static function getEventTypesByResource(): array
    {
        return WebhookEventTypes::getEventTypesByResource();
    }

    /**
     * 保存前自动设置 listen_all_events 字段
     */
    protected static function booted(): void
    {
        static::saving(function (WebhookEndpoint $endpoint) {
            $eventTypes = $endpoint->event_types ?? [];
            $endpoint->listen_all_events = in_array(WebhookEventTypes::ALL_EVENTS, $eventTypes);
        });
    }
}
