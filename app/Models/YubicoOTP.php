<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class YubicoOTP extends Model
{
    protected $table = 'yubico_otps';

    protected $fillable = [
        'device_id',
        'yubico_otpable_type',
        'yubico_otpable_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    public function yubicoOtpable(): MorphTo
    {
        return $this->morphTo('yubicoOtpable');
    }
}
