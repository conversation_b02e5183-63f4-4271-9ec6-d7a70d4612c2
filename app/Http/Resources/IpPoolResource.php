<?php

namespace App\Http\Resources;

use App\Models\IpPool;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use PhpIP\IPv4Block;
use PhpIP\IPv6Block;

class IpPoolResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var IpPool $this */
        $stats = $this->calculateStats();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'ip_version' => $this->ip_version,
            // 'ip_version_label' => IpPool::getAvailableIpVersions()[$this->ip_version] ?? $this->ip_version,
            'sharing_strategy' => $this->sharing_strategy,
            // 'sharing_strategy_label' => IpPool::getAvailableSharingStrategies()[$this->sharing_strategy] ?? $this->sharing_strategy,
            // 'driver' => $this->driver,
            'stats' => $stats,
            'is_available' => $stats['available_ips'] > 0,
            'utilization_rate' => $stats['utilization_rate'],
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }

    /**
     * 计算IP池的统计信息
     */
    protected function calculateStats(): array
    {
        $totalIps = 0;
        $usedIps = 0;
        $availableIps = 0;

        // 根据IP版本计算不同的统计信息
        if ($this->supportsIpv4()) {
            $ipv4Stats = $this->calculateIpv4Stats();
            $totalIps += $ipv4Stats['total'];
            $usedIps += $ipv4Stats['used'];
            $availableIps += $ipv4Stats['available'];
        }

        if ($this->supportsIpv6()) {
            $ipv6Stats = $this->calculateIpv6Stats();
            $totalIps += $ipv6Stats['total'];
            $usedIps += $ipv6Stats['used'];
            $availableIps += $ipv6Stats['available'];
        }

        $utilizationRate = $totalIps > 0 ? round(($usedIps / $totalIps) * 100, 2) : 0;

        return [
            'total_ips' => $totalIps,
            'used_ips' => $usedIps,
            'available_ips' => $availableIps,
            'utilization_rate' => $utilizationRate,
        ];
    }

    /**
     * 计算IPv4统计信息
     */
    protected function calculateIpv4Stats(): array
    {
        if ($this->subnet_v4) {
            try {
                // 使用rlanvin/php-ip库计算子网中的IP数量
                $subnet = new IPv4Block($this->subnet_v4);
                $totalIps = (int) $subnet->getNbAddresses();

                // 减去网络地址和广播地址
                if ($totalIps > 2) {
                    $totalIps -= 2;
                }
            } catch (\Exception $e) {
                // 回退方案：计算实际的 IPv4 地址数量
                $totalIps = $this->poolIps()->get()->filter(function ($poolIp) {
                    return $poolIp->getIpType() === 'IPv4';
                })->count();
            }
        } else {
            // 计算实际的 IPv4 地址数量
            $totalIps = $this->poolIps()->get()->filter(function ($poolIp) {
                return $poolIp->getIpType() === 'IPv4';
            })->count();
        }

        // 计算已使用的 IPv4 地址数量
        $usedIps = $this->poolIps()
            ->where('usage_count', '>', 0)
            ->get()
            ->filter(function ($poolIp) {
                return $poolIp->getIpType() === 'IPv4';
            })->count();

        return [
            'total' => $totalIps,
            'used' => $usedIps,
            'available' => $totalIps - $usedIps,
        ];
    }

    /**
     * 计算IPv6统计信息
     */
    protected function calculateIpv6Stats(): array
    {
        if ($this->subnet_v6) {
            try {
                // 对于IPv6，由于地址空间巨大，我们使用实际生成的IP数量
                // 但也可以尝试计算理论上的地址数量（如果子网前缀比较大的话）
                $subnet = new IPv6Block($this->subnet_v6);
                $prefixLength = $subnet->getPrefixLength();

                // 如果前缀长度较大（比如 /64 或更大），可以计算理论地址数量
                // 否则数量会非常巨大，使用实际生成的IP数量
                if ($prefixLength >= 64) {
                    $totalIps = (int) $subnet->getNbAddresses();
                } else {
                    // 地址空间太大，使用实际生成的IP数量
                    $totalIps = $this->poolIps()->get()->filter(function ($poolIp) {
                        return $poolIp->getIpType() === 'IPv6';
                    })->count();
                }
            } catch (\Exception $e) {
                $totalIps = $this->poolIps()->get()->filter(function ($poolIp) {
                    return $poolIp->getIpType() === 'IPv6';
                })->count();
            }
        } else {
            $totalIps = $this->poolIps()->get()->filter(function ($poolIp) {
                return $poolIp->getIpType() === 'IPv6';
            })->count();
        }

        // 计算已使用的 IPv6 地址数量
        $usedIps = $this->poolIps()
            ->where('usage_count', '>', 0)
            ->get()
            ->filter(function ($poolIp) {
                return $poolIp->getIpType() === 'IPv6';
            })->count();

        return [
            'total' => $totalIps,
            'used' => $usedIps,
            'available' => $totalIps - $usedIps,
        ];
    }
}
