<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WebhookDeliveryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'event_type' => $this->event_type,
            'resource_type' => $this->resource_type,
            'resource_name' => $this->resource_name,
            'namespace' => $this->namespace,
            'cluster_name' => $this->cluster_name,
            'cluster_id' => $this->cluster_id,
            'status' => $this->status,
            'attempts' => $this->attempts,
            'response_status' => $this->response_status,
            'response_body' => $this->response_body,
            'error_message' => $this->error_message,
            'payload' => $this->payload,
            'delivered_at' => $this->delivered_at,
            'failed_at' => $this->failed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
