<?php

namespace App\Http\Controllers;

use App\Models\WebhookEndpoint;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WebhookController extends Controller
{
    /**
     * 显示 webhook 管理页面
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        return Inertia::render('Webhooks/Index', [
            'workspace' => $workspace,
            'resourceTypes' => WebhookEndpoint::getAvailableResourceTypes(),
            'eventTypes' => WebhookEndpoint::getAvailableEventTypes(),
        ]);
    }

    /**
     * 显示创建 webhook 页面
     */
    public function create(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        return Inertia::render('Webhooks/Create', [
            'workspace' => $workspace,
            'resourceTypes' => WebhookEndpoint::getAvailableResourceTypes(),
            'eventTypes' => WebhookEndpoint::getAvailableEventTypes(),
        ]);
    }

    /**
     * 显示编辑 webhook 页面
     */
    public function edit(Request $request, WebhookEndpoint $webhookEndpoint)
    {
        $this->authorize('update', $webhookEndpoint);

        $workspace = $request->user()->getWorkspace();

        return Inertia::render('Webhooks/Edit', [
            'webhook' => $webhookEndpoint,
            'workspace' => $workspace,
            'resourceTypes' => WebhookEndpoint::getAvailableResourceTypes(),
            'eventTypes' => WebhookEndpoint::getAvailableEventTypes(),
        ]);
    }

    /**
     * 显示 webhook 详情页面
     */
    public function show(Request $request, WebhookEndpoint $webhookEndpoint)
    {
        $this->authorize('view', $webhookEndpoint);

        $workspace = $request->user()->getWorkspace();

        $webhookEndpoint->loadCount('deliveries');
        $webhookEndpoint->load('recentDeliveries');

        return Inertia::render('Webhooks/Show', [
            'webhook' => $webhookEndpoint,
            'workspace' => $workspace,
        ]);
    }
}
