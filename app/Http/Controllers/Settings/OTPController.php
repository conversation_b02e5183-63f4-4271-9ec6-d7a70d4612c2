<?php

namespace App\Http\Controllers\Settings;

use App\Contracts\YubicoOTP;
use App\Exceptions\Device\DeviceAlreadyBoundException;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class OTPController extends Controller
{
    /**
     * 显示 OTP 设置页面
     */
    public function index()
    {
        $user = Auth::user();

        return Inertia::render('settings/OTP', [
            'devices' => $user->yubicoOtps->map(function ($device) {
                return [
                    'id' => $device->id,
                    'device_id' => $device->device_id,
                    'created_at' => $device->created_at->format('Y-m-d H:i:s'),
                ];
            }),
        ]);
    }

    /**
     * 添加新的 OTP 设备
     */
    public function store(Request $request)
    {
        $request->validate([
            'otp' => 'required|max:50',
        ]);

        $user = Auth::user();
        $otp = app(YubicoOTP::class);
        $otp->setOTP($request->input('otp'));

        if (! $otp->verify()) {
            return back()->withErrors([
                'otp' => 'OTP 验证失败，请重试',
            ]);
        }

        $device_id = $otp->getDeviceID();

        try {
            $user->addYubicoOTPDevice($device_id);

            return redirect()->back()->with('success', 'OTP 设备添加成功');
        } catch (DeviceAlreadyBoundException $e) {
            return back()->withErrors([
                'otp' => '该设备已被其他用户绑定',
            ]);
        }
    }

    /**
     * 删除 OTP 设备
     */
    public function destroy(string $device_id)
    {
        $user = Auth::user();

        $deleted = $user->removeYubicoOTPDevice($device_id);

        if ($deleted) {
            return redirect()->back()->with('success', 'OTP 设备已成功删除');
        }

        return redirect()->back()->with('error', 'OTP 设备删除失败');
    }
}
