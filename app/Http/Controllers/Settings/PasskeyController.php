<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use <PERSON>tie\LaravelPasskeys\Actions\GeneratePasskeyRegisterOptionsAction;
use Spatie\LaravelPasskeys\Actions\StorePasskeyAction;
use Throwable;

class PasskeyController extends Controller
{
    /**
     * Display passkey settings page
     */
    public function index()
    {
        $user = Auth::user();

        return Inertia::render('settings/Passkey', [
            'passkeys' => $user->passkeys()
                ->get()
                ->map(fn ($key) => $key->only(['id', 'name', 'last_used_at'])),
        ]);
    }

    /**
     * Generate passkey registration options
     */
    public function generateOptions()
    {
        $generatePassKeyOptionsAction = app(GeneratePasskeyRegisterOptionsAction::class);

        return $generatePassKeyOptionsAction->execute(Auth::user());
    }

    /**
     * Store a new passkey
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'passkey' => 'required|json',
            'options' => 'required|json',
            'name' => 'nullable|string|max:255',
        ]);

        $user = Auth::user();
        $storePasskeyAction = app(StorePasskeyAction::class);

        try {
            $storePasskeyAction->execute(
                $user,
                $data['passkey'],
                $data['options'],
                $request->getHost(),
                ['name' => $data['name'] ?? '通行密钥 '.Str::random(6)],
            );

            return redirect()->back()->with('success', '通行密钥已成功创建');

        } catch (Throwable $e) {
            throw ValidationException::withMessages([
                'passkey' => '创建通行密钥时出现错误，请重试',
            ]);
        }
    }

    /**
     * Delete a passkey
     */
    public function destroy(string $id)
    {
        $user = Auth::user();

        $deleted = $user->passkeys()->where('id', $id)->delete();

        if ($deleted) {
            return redirect()->back()->with('success', '通行密钥已成功删除');
        }

        return redirect()->back()->with('error', '通行密钥删除失败');
    }
}
