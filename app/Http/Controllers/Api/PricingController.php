<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ResourcePricing;
use App\Service\PricingHistoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PricingController extends Controller
{
    protected PricingHistoryService $pricingHistoryService;

    public function __construct(PricingHistoryService $pricingHistoryService)
    {
        $this->pricingHistoryService = $pricingHistoryService;
    }

    /**
     * 获取资源类型的单位类型配置
     */
    public function getUnitTypes(Request $request)
    {
        $resourceType = $request->get('resource_type');

        if (! $resourceType) {
            return $this->badRequest('resource_type parameter is required');
        }

        $unitTypes = config("pricable.unit_types.{$resourceType}", []);

        if (empty($unitTypes)) {
            return $this->notFound("Resource type '{$resourceType}' not found or has no unit types configured");
        }

        return $this->success([
            'resource_type' => $resourceType,
            'unit_types' => $unitTypes,
        ]);
    }

    /**
     * 获取资源的当前价格信息
     */
    public function getResourcePricing(Request $request, string $resourceType, int $resourceId)
    {
        try {
            // 验证资源类型
            $models = config('pricable.models', []);
            $modelClass = null;

            foreach ($models as $class => $type) {
                if ($type === $resourceType) {
                    $modelClass = $class;
                    break;
                }
            }

            if (! $modelClass || ! class_exists($modelClass)) {
                return $this->badRequest("Invalid resource type: {$resourceType}");
            }

            // 获取资源实例
            $resource = $modelClass::find($resourceId);
            if (! $resource) {
                return $this->notFound("Resource not found: {$resourceType} #{$resourceId}");
            }

            // 获取当前价格信息
            $currentPricing = ResourcePricing::getAllCurrentPricingForResource($resource);
            $unitTypes = config("pricable.unit_types.{$resourceType}", []);

            $pricing = [];
            foreach ($unitTypes as $unitType => $config) {
                $resourcePricing = $currentPricing[$unitType] ?? null;
                $pricing[$unitType] = [
                    'unit_type' => $unitType,
                    'name' => $config['name'] ?? ucfirst($unitType),
                    'description' => $config['description'] ?? '',
                    'has_pricing' => ! is_null($resourcePricing),
                    'price_per_month' => $resourcePricing?->price_per_unit_per_month ?? config('pricable.default_cost'),
                    'price_per_minute' => $resourcePricing?->price_per_minute ?? config('pricable.default_cost'),
                    'effective_date' => $resourcePricing?->effective_date?->toISOString(),
                ];
            }

            return $this->success([
                'resource_type' => $resourceType,
                'resource_id' => $resourceId,
                'resource_name' => $resource->name ?? "#{$resourceId}",
                'pricing' => $pricing,
                'billing_enabled' => method_exists($resource, 'isBillingEnabled') ? $resource->isBillingEnabled() : ! empty($currentPricing),
            ]);

        } catch (\Exception $e) {
            Log::error('获取资源价格信息失败', [
                'resource_type' => $resourceType,
                'resource_id' => $resourceId,
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('获取价格信息失败');
        }
    }

    /**
     * 计算资源使用费用
     */
    public function calculateCost(Request $request, string $resourceType, int $resourceId)
    {
        try {
            // 验证资源类型
            $models = config('pricable.models', []);
            $modelClass = null;

            foreach ($models as $class => $type) {
                if ($type === $resourceType) {
                    $modelClass = $class;
                    break;
                }
            }

            if (! $modelClass || ! class_exists($modelClass)) {
                return $this->badRequest("Invalid resource type: {$resourceType}");
            }

            // 获取资源实例
            $resource = $modelClass::find($resourceId);
            if (! $resource) {
                return $this->notFound("Resource not found: {$resourceType} #{$resourceId}");
            }

            // 验证计费是否启用
            if (method_exists($resource, 'isBillingEnabled') && ! $resource->isBillingEnabled()) {
                return $this->unprocessableEntity('该资源未启用计费或未配置价格');
            }

            // 获取使用量参数
            $usage = $request->get('usage', []);
            $minutes = max(1, (int) $request->get('minutes', 1));

            if (empty($usage)) {
                return $this->badRequest('usage parameter is required');
            }

            // 获取当前价格信息
            $currentPricing = ResourcePricing::getAllCurrentPricingForResource($resource);

            if (empty($currentPricing)) {
                return $this->unprocessableEntity('该资源未配置价格');
            }

            // 计算费用
            $breakdown = [];
            $totalCost = config('pricable.default_cost');

            foreach ($usage as $unitType => $usageAmount) {
                if (! isset($currentPricing[$unitType])) {
                    continue;
                }

                $usageAmount = (float) $usageAmount;
                if ($usageAmount <= 0) {
                    continue;
                }

                $pricing = $currentPricing[$unitType];
                $costPerMinute = $pricing->calculateCostPerMinute($usageAmount);
                $totalCostForUnit = bcmul($costPerMinute, (string) $minutes, 8);
                $monthlyCostForUnit = bcmul($pricing->price_per_unit_per_month, (string) $usageAmount, 8);

                $breakdown[$unitType] = [
                    'usage' => $usageAmount,
                    'price_per_month' => $pricing->price_per_unit_per_month,
                    'price_per_minute' => $pricing->price_per_minute,
                    'cost_per_minute' => $costPerMinute,
                    'minutes' => $minutes,
                    'total_cost' => $totalCostForUnit,
                    'monthly_cost' => $monthlyCostForUnit,
                ];

                $totalCost = bcadd($totalCost, $totalCostForUnit, 8);
            }

            // 计算不同时间周期的费用
            $costPerMinute = bcdiv($totalCost, (string) $minutes, 8);
            $costPerHour = bcmul($costPerMinute, '60', 8);
            $costPerDay = bcmul($costPerHour, '24', 8);

            // 计算真实的月费用：直接使用单位价格乘以使用量
            $realMonthlyTotal = config('pricable.default_cost');
            foreach ($usage as $unitType => $usageAmount) {
                if (! isset($currentPricing[$unitType])) {
                    continue;
                }
                $usageAmount = (float) $usageAmount;
                if ($usageAmount <= 0) {
                    continue;
                }
                $pricing = $currentPricing[$unitType];
                $monthlyUnitCost = bcmul($pricing->price_per_unit_per_month, (string) $usageAmount, 8);
                $realMonthlyTotal = bcadd($realMonthlyTotal, $monthlyUnitCost, 8);
            }

            $costPerMonth = $realMonthlyTotal;

            return $this->success([
                'resource_type' => $resourceType,
                'resource_id' => $resourceId,
                'resource_name' => $resource->name ?? "#{$resourceId}",
                'usage' => $usage,
                'minutes' => $minutes,
                'breakdown' => $breakdown,
                'total' => [
                    'per_minute' => $costPerMinute,
                    'per_hour' => $costPerHour,
                    'per_day' => $costPerDay,
                    'per_month' => $costPerMonth,
                ],
                'formatted' => [
                    'per_minute' => formatAmount($costPerMinute, true, 4),
                    'per_hour' => formatAmount($costPerHour),
                    'per_day' => formatAmount($costPerDay),
                    'per_month' => formatAmount($costPerMonth),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('计算资源费用失败', [
                'resource_type' => $resourceType,
                'resource_id' => $resourceId,
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('计算费用失败');
        }
    }

    /**
     * 获取所有支持的资源类型
     */
    public function getSupportedResourceTypes()
    {
        $models = config('pricable.models', []);
        $unitTypes = config('pricable.unit_types', []);

        $resourceTypes = [];
        foreach ($models as $modelClass => $resourceType) {
            if (! class_exists($modelClass)) {
                continue;
            }

            $resourceTypes[] = [
                'type' => $resourceType,
                'model_class' => $modelClass,
                'display_name' => $this->getResourceTypeDisplayName($resourceType),
                'unit_types' => array_keys($unitTypes[$resourceType] ?? []),
            ];
        }

        return $this->success([
            'resource_types' => $resourceTypes,
        ]);
    }

    /**
     * 获取资源类型显示名称
     */
    protected function getResourceTypeDisplayName(string $resourceType): string
    {
        return match ($resourceType) {
            'cluster' => '集群资源',
            'ip' => 'IP池资源',
            'gpu' => 'GPU资源',
            default => ucfirst($resourceType).'资源',
        };
    }
}
