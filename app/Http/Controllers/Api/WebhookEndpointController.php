<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\WebhookDeliveryResource;
use App\Http\Resources\WebhookEndpointResource;
use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use App\Service\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class WebhookEndpointController extends Controller
{
    protected WebhookService $webhookService;

    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        $webhooks = WebhookEndpoint::where('workspace_id', $workspace->id)
            ->withCount('deliveries')
            ->with('recentDeliveries')
            ->latest()
            ->get();

        // 计算成功率
        $webhooks->each(function ($webhook) {
            $totalDeliveries = $webhook->deliveries_count;
            if ($totalDeliveries > 0) {
                $successfulDeliveries = $webhook->deliveries()
                    ->where('status', WebhookDelivery::STATUS_SUCCESS)
                    ->count();
                $webhook->success_rate = round(($successfulDeliveries / $totalDeliveries) * 100, 1);
            } else {
                $webhook->success_rate = null;
            }
        });

        return WebhookEndpointResource::collection($webhooks);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'secret' => 'nullable|string|max:255',
            'event_types' => 'required|array|min:1',
            'event_types.*' => [
                'required',
                'string',
                Rule::in(WebhookEndpoint::getAvailableEventTypes())
            ],
            'description' => 'nullable|string|max:1000',
            'timeout' => 'nullable|integer|min:5|max:300',
            'max_retries' => 'nullable|integer|min:0|max:10',
        ]);

        // 自动设置 listen_all_events 标志
        $listenAllEvents = in_array('all', $validated['event_types']);

        $webhook = WebhookEndpoint::create([
            'workspace_id' => $workspace->id,
            'name' => $validated['name'],
            'url' => $validated['url'],
            'secret' => $validated['secret'],
            'event_types' => $validated['event_types'],
            'listen_all_events' => $listenAllEvents,
            'description' => $validated['description'],
            'timeout' => $validated['timeout'] ?? 30,
            'max_retries' => $validated['max_retries'] ?? 3,
            'is_active' => true,
        ]);

        Log::info('Webhook 端点已创建', [
            'webhook_id' => $webhook->id,
            'workspace_id' => $workspace->id,
            'name' => $webhook->name,
            'url' => $webhook->url,
        ]);

        return new WebhookEndpointResource($webhook);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, WebhookEndpoint $webhookEndpoint)
    {
        $this->authorize('view', $webhookEndpoint);

        $webhookEndpoint->loadCount('deliveries');
        $webhookEndpoint->load('recentDeliveries');

        // 计算成功率
        $totalDeliveries = $webhookEndpoint->deliveries_count;
        if ($totalDeliveries > 0) {
            $successfulDeliveries = $webhookEndpoint->deliveries()
                ->where('status', WebhookDelivery::STATUS_SUCCESS)
                ->count();
            $webhookEndpoint->success_rate = round(($successfulDeliveries / $totalDeliveries) * 100, 1);
        } else {
            $webhookEndpoint->success_rate = null;
        }

        return new WebhookEndpointResource($webhookEndpoint);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, WebhookEndpoint $webhookEndpoint)
    {
        $this->authorize('update', $webhookEndpoint);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'secret' => 'nullable|string|max:255',
            'event_types' => 'required|array|min:1',
            'event_types.*' => [
                'required',
                'string',
                Rule::in(WebhookEndpoint::getAvailableEventTypes())
            ],
            'description' => 'nullable|string|max:1000',
            'timeout' => 'nullable|integer|min:5|max:300',
            'max_retries' => 'nullable|integer|min:0|max:10',
            'is_active' => 'boolean',
        ]);

        // 自动设置 listen_all_events 标志
        if (isset($validated['event_types'])) {
            $validated['listen_all_events'] = in_array('all', $validated['event_types']);
        }

        $webhookEndpoint->update($validated);

        Log::info('Webhook 端点已更新', [
            'webhook_id' => $webhookEndpoint->id,
            'name' => $webhookEndpoint->name,
        ]);

        return new WebhookEndpointResource($webhookEndpoint);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, WebhookEndpoint $webhookEndpoint)
    {
        $this->authorize('delete', $webhookEndpoint);

        $webhookEndpoint->delete();

        Log::info('Webhook 端点已删除', [
            'webhook_id' => $webhookEndpoint->id,
            'name' => $webhookEndpoint->name,
        ]);

        return response()->json(['message' => 'Webhook 端点已删除']);
    }

    /**
     * 测试 webhook 端点
     */
    public function test(Request $request, WebhookEndpoint $webhookEndpoint)
    {
        $this->authorize('update', $webhookEndpoint);

        $result = $this->webhookService->testWebhook($webhookEndpoint);

        return response()->json($result);
    }

    /**
     * 获取 webhook 端点的发送历史
     */
    public function deliveries(Request $request, WebhookEndpoint $webhookEndpoint)
    {
        $this->authorize('view', $webhookEndpoint);

        $validated = $request->validate([
            'status' => 'nullable|string|in:pending,success,failed',
            'resource_type' => 'nullable|string',
            'event_type' => 'nullable|string',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = $webhookEndpoint->deliveries();

        if (!empty($validated['status'])) {
            $query->where('status', $validated['status']);
        }

        if (!empty($validated['resource_type'])) {
            $query->where('resource_type', $validated['resource_type']);
        }

        if (!empty($validated['event_type'])) {
            $query->where('event_type', $validated['event_type']);
        }

        $deliveries = $query->latest()
            ->paginate($validated['per_page'] ?? 20);

        return WebhookDeliveryResource::collection($deliveries);
    }

    /**
     * 重新发送失败的 webhook
     */
    public function resend(Request $request, WebhookEndpoint $webhookEndpoint, WebhookDelivery $delivery)
    {
        $this->authorize('update', $webhookEndpoint);

        if ($delivery->webhook_endpoint_id !== $webhookEndpoint->id) {
            return response()->json(['message' => '发送记录不属于该 webhook 端点'], 400);
        }

        if (!$delivery->isFailed()) {
            return response()->json(['message' => '只能重新发送失败的 webhook'], 400);
        }

        try {
            $this->webhookService->resendWebhook($delivery);
            return response()->json(['message' => 'Webhook 已重新发送']);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * 获取可用的事件类型
     */
    public function options()
    {
        return response()->json([
            'event_types' => WebhookEndpoint::getAvailableEventTypesWithLabels(),
            'event_types_grouped' => WebhookEndpoint::getEventTypesByResource(),
        ]);
    }
}
