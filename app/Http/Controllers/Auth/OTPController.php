<?php

namespace App\Http\Controllers\Auth;

use App\Contracts\YubicoOTP;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class OTPController extends Controller
{
    /**
     * 显示 OTP 登录页面
     */
    public function showLoginForm(): Response|RedirectResponse
    {
        if (Auth::guard('web')->check()) {
            return redirect()->intended(config('passkeys.redirect_to_after_login'));
        }

        return Inertia::render('auth/OTPLogin');
    }

    /**
     * 处理 OTP 登录请求
     */
    public function login(Request $request): RedirectResponse
    {
        $request->validate([
            'otp' => 'required|max:50',
        ]);

        $otp = app(YubicoOTP::class);
        $otp->setOTP($request->input('otp'));
        $device_id = $otp->getDeviceID();

        $user = (new User)->findByDeviceId($device_id);
        if (! $user) {
            return back()->withErrors([
                'otp' => '设备不存在或未注册',
            ]);
        }

        if (! $otp->verify()) {
            return back()->withErrors([
                'otp' => 'OTP 验证失败',
            ]);
        }

        Auth::guard('web')->login($user, true);

        return redirect()->intended(config('passkeys.redirect_to_after_login'));
    }
}
