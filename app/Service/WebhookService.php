<?php

namespace App\Service;

use App\Events\K8s\BaseK8sEvent;
use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookServer\WebhookCall;

class WebhookService
{
    /**
     * 发送细化的 K8s 事件 webhook
     */
    public function sendK8sEventWebhook(WebhookEndpoint $endpoint, BaseK8sEvent $k8sEvent): void
    {
        try {
            // 创建发送记录
            $delivery = $this->createK8sEventDeliveryRecord($endpoint, $k8sEvent);

            // 获取 webhook payload
            $payload = $k8sEvent->getWebhookPayload();

            // 更新发送记录的 payload
            $delivery->update(['payload' => $payload]);

            // 发送 webhook
            $webhookCall = WebhookCall::create()
                ->url($endpoint->url)
                ->payload($payload)
                ->useSecret($endpoint->secret)
                ->timeoutInSeconds($endpoint->timeout)
                ->maximumTries($endpoint->max_retries);

            // 设置自定义头部
            $webhookCall->withHeaders([
                'X-Webhook-Event' => $k8sEvent->getWebhookEventType(),
                'X-Webhook-Action' => $k8sEvent->getAction(),
                'X-Webhook-Resource-Type' => $k8sEvent->resourceType,
                'X-Webhook-Namespace' => $k8sEvent->namespace,
                'X-Webhook-Cluster' => $k8sEvent->clusterName,
                'User-Agent' => 'PaaS-Webhook/2.0',
            ]);

            // 异步发送
            $webhookCall->dispatch();

            Log::info('K8s 事件 Webhook 已排队发送', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'event_type' => $k8sEvent->getWebhookEventType(),
                'action' => $k8sEvent->getAction(),
                'resource_type' => $k8sEvent->resourceType,
                'resource_name' => $k8sEvent->resourceName,
                'namespace' => $k8sEvent->namespace,
            ]);

        } catch (\Exception $e) {
            Log::error('K8s 事件 Webhook 发送失败', [
                'endpoint_id' => $endpoint->id,
                'error' => $e->getMessage(),
                'event_type' => $k8sEvent->getWebhookEventType(),
                'resource_type' => $k8sEvent->resourceType,
                'resource_name' => $k8sEvent->resourceName,
            ]);

            // 如果已创建发送记录，标记为失败
            if (isset($delivery)) {
                $delivery->markAsFailed($e->getMessage());
            }
        }
    }



    /**
     * 创建 K8s 事件发送记录
     */
    protected function createK8sEventDeliveryRecord(
        WebhookEndpoint $endpoint,
        BaseK8sEvent $k8sEvent
    ): WebhookDelivery {
        return WebhookDelivery::create([
            'webhook_endpoint_id' => $endpoint->id,
            'event_type' => $k8sEvent->getWebhookEventType(),
            'resource_type' => $k8sEvent->resourceType,
            'resource_name' => $k8sEvent->resourceName,
            'namespace' => $k8sEvent->namespace,
            'cluster_name' => $k8sEvent->clusterName,
            'cluster_id' => $k8sEvent->clusterId,
            'payload' => [], // 稍后更新
            'status' => WebhookDelivery::STATUS_PENDING,
        ]);
    }



    /**
     * 处理 webhook 发送结果（由 webhook job 调用）
     */
    public function handleWebhookResult(
        int $deliveryId,
        bool $success,
        ?int $responseStatus = null,
        ?string $responseBody = null,
        ?string $errorMessage = null
    ): void {
        $delivery = WebhookDelivery::find($deliveryId);
        if (!$delivery) {
            return;
        }

        $delivery->incrementAttempts();

        if ($success) {
            $delivery->markAsSuccessful($responseStatus, $responseBody);
            Log::info('Webhook 发送成功', [
                'delivery_id' => $deliveryId,
                'response_status' => $responseStatus,
            ]);
        } else {
            $delivery->markAsFailed($errorMessage, $responseStatus, $responseBody);
            Log::warning('Webhook 发送失败', [
                'delivery_id' => $deliveryId,
                'error' => $errorMessage,
                'response_status' => $responseStatus,
            ]);
        }
    }

    /**
     * 重新发送失败的 webhook
     */
    public function resendWebhook(WebhookDelivery $delivery): void
    {
        if (!$delivery->isFailed()) {
            throw new \InvalidArgumentException('只能重新发送失败的 webhook');
        }

        $endpoint = $delivery->webhookEndpoint;
        if (!$endpoint || !$endpoint->is_active) {
            throw new \InvalidArgumentException('Webhook 端点不存在或已禁用');
        }

        // 重置状态
        $delivery->update([
            'status' => WebhookDelivery::STATUS_PENDING,
            'error_message' => null,
            'failed_at' => null,
        ]);

        // 重新发送
        $webhookCall = WebhookCall::create()
            ->url($endpoint->url)
            ->payload($delivery->payload)
            ->useSecret($endpoint->secret)
            ->timeoutInSeconds($endpoint->timeout)
            ->maximumTries($endpoint->max_retries);

        $webhookCall->withHeaders([
            'X-Webhook-Event' => $delivery->event_type,
            'X-Webhook-Resource-Type' => $delivery->resource_type,
            'X-Webhook-Namespace' => $delivery->namespace,
            'X-Webhook-Cluster' => $delivery->cluster_name,
            'User-Agent' => 'PaaS-Webhook/1.0',
        ]);

        $webhookCall->dispatch();

        Log::info('Webhook 重新发送', [
            'delivery_id' => $delivery->id,
            'endpoint_id' => $endpoint->id,
        ]);
    }

    /**
     * 测试 webhook 端点
     */
    public function testWebhook(WebhookEndpoint $endpoint): array
    {
        try {
            $testPayload = [
                'event' => [
                    'type' => 'test',
                    'timestamp' => now()->toISOString(),
                    'id' => uniqid('test_', true),
                ],
                'message' => 'This is a test webhook from PaaS platform',
                'workspace' => [
                    'namespace' => $endpoint->workspace->namespace,
                ],
            ];

            $webhookCall = WebhookCall::create()
                ->url($endpoint->url)
                ->payload($testPayload)
                ->useSecret($endpoint->secret)
                ->timeoutInSeconds($endpoint->timeout)
                ->maximumTries(1);

            $webhookCall->withHeaders([
                'X-Webhook-Event' => 'test',
                'User-Agent' => 'PaaS-Webhook/1.0',
            ]);

            // 同步发送测试请求
            $response = $webhookCall->dispatchSync();

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'response_body' => $response->getBody()->getContents(),
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
