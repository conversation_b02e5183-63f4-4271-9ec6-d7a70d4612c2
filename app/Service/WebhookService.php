<?php

namespace App\Service;

use App\Events\K8s\BaseK8sEvent;
use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookServer\WebhookCall;

class WebhookService
{
    /**
     * 发送细化的 K8s 事件 webhook
     */
    public function sendK8sEventWebhook(WebhookEndpoint $endpoint, BaseK8sEvent $k8sEvent): void
    {
        try {
            // 创建发送记录
            $delivery = $this->createK8sEventDeliveryRecord($endpoint, $k8sEvent);

            // 获取 webhook payload
            $payload = $k8sEvent->getWebhookPayload();

            // 更新发送记录的 payload
            $delivery->update(['payload' => $payload]);

            // 发送 webhook
            $webhookCall = WebhookCall::create()
                ->url($endpoint->url)
                ->payload($payload)
                ->timeoutInSeconds($endpoint->timeout)
                ->maximumTries($endpoint->max_retries);

            // 只有当 secret 不为空时才设置
            if (!empty($endpoint->secret)) {
                $webhookCall->useSecret($endpoint->secret);
            }

            // 设置自定义头部
            $webhookCall->withHeaders([
                'X-Webhook-Event' => $k8sEvent->getWebhookEventType(),
                'X-Webhook-Action' => $k8sEvent->getAction(),
                'X-Webhook-Resource-Type' => $k8sEvent->resourceType,
                'X-Webhook-Namespace' => $k8sEvent->namespace,
                'X-Webhook-Cluster' => $k8sEvent->clusterName,
                'User-Agent' => 'PaaS-Webhook/2.0',
            ]);

            // 异步发送
            $webhookCall->dispatch();

            Log::info('K8s 事件 Webhook 已排队发送', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'event_type' => $k8sEvent->getWebhookEventType(),
                'action' => $k8sEvent->getAction(),
                'resource_type' => $k8sEvent->resourceType,
                'resource_name' => $k8sEvent->resourceName,
                'namespace' => $k8sEvent->namespace,
            ]);

        } catch (\Exception $e) {
            Log::error('K8s 事件 Webhook 发送失败', [
                'endpoint_id' => $endpoint->id,
                'error' => $e->getMessage(),
                'event_type' => $k8sEvent->getWebhookEventType(),
                'resource_type' => $k8sEvent->resourceType,
                'resource_name' => $k8sEvent->resourceName,
            ]);

            // 如果已创建发送记录，标记为失败
            if (isset($delivery)) {
                $delivery->markAsFailed($e->getMessage());
            }
        }
    }



    /**
     * 创建 K8s 事件发送记录
     */
    protected function createK8sEventDeliveryRecord(
        WebhookEndpoint $endpoint,
        BaseK8sEvent $k8sEvent
    ): WebhookDelivery {
        return WebhookDelivery::create([
            'webhook_endpoint_id' => $endpoint->id,
            'event_type' => $k8sEvent->getWebhookEventType(),
            'resource_name' => $k8sEvent->resourceName,
            'namespace' => $k8sEvent->namespace,
            'cluster_name' => $k8sEvent->clusterName,
            'cluster_id' => $k8sEvent->clusterId,
            'payload' => [], // 稍后更新
            'status' => WebhookDelivery::STATUS_PENDING,
        ]);
    }



    /**
     * 处理 webhook 发送结果（由 webhook job 调用）
     */
    public function handleWebhookResult(
        int $deliveryId,
        bool $success,
        ?int $responseStatus = null,
        ?string $responseBody = null,
        ?string $errorMessage = null
    ): void {
        $delivery = WebhookDelivery::find($deliveryId);
        if (!$delivery) {
            return;
        }

        $delivery->incrementAttempts();

        if ($success) {
            $delivery->markAsSuccessful($responseStatus, $responseBody);
            Log::info('Webhook 发送成功', [
                'delivery_id' => $deliveryId,
                'response_status' => $responseStatus,
            ]);
        } else {
            $delivery->markAsFailed($errorMessage, $responseStatus, $responseBody);
            Log::warning('Webhook 发送失败', [
                'delivery_id' => $deliveryId,
                'error' => $errorMessage,
                'response_status' => $responseStatus,
            ]);
        }
    }

    /**
     * 重新发送失败的 webhook
     */
    public function resendWebhook(WebhookDelivery $delivery): void
    {
        if (!$delivery->isFailed()) {
            throw new \InvalidArgumentException('只能重新发送失败的 webhook');
        }

        $endpoint = $delivery->webhookEndpoint;
        if (!$endpoint || !$endpoint->is_active) {
            throw new \InvalidArgumentException('Webhook 端点不存在或已禁用');
        }

        // 重置状态
        $delivery->update([
            'status' => WebhookDelivery::STATUS_PENDING,
            'error_message' => null,
            'failed_at' => null,
        ]);

        // 重新发送
        $webhookCall = WebhookCall::create()
            ->url($endpoint->url)
            ->payload($delivery->payload)
            ->timeoutInSeconds($endpoint->timeout)
            ->maximumTries($endpoint->max_retries);

        // 只有当 secret 不为空时才设置
        if (!empty($endpoint->secret)) {
            $webhookCall->useSecret($endpoint->secret);
        }

        $webhookCall->withHeaders([
            'X-Webhook-Event' => $delivery->event_type,
            'X-Webhook-Resource-Type' => $delivery->resource_type,
            'X-Webhook-Namespace' => $delivery->namespace,
            'X-Webhook-Cluster' => $delivery->cluster_name,
            'User-Agent' => 'PaaS-Webhook/1.0',
        ]);

        $webhookCall->dispatch();

        Log::info('Webhook 重新发送', [
            'delivery_id' => $delivery->id,
            'endpoint_id' => $endpoint->id,
        ]);
    }

    /**
     * 测试 webhook 端点
     */
    public function testWebhook(WebhookEndpoint $endpoint): array
    {
        try {
            $testPayload = [
                'event' => [
                    'type' => 'test',
                    'timestamp' => now()->toISOString(),
                    'id' => uniqid('test_', true),
                ],
                'message' => 'This is a test webhook from PaaS platform',
                'webhook' => [
                    'name' => $endpoint->name,
                    'url' => $endpoint->url,
                ],
            ];

            // 如果有 workspace 关系，添加 workspace 信息
            if ($endpoint->workspace) {
                $testPayload['workspace'] = [
                    'namespace' => $endpoint->workspace->namespace,
                ];
            }

            $webhookCall = WebhookCall::create()
                ->url($endpoint->url)
                ->payload($testPayload)
                ->timeoutInSeconds($endpoint->timeout)
                ->maximumTries(1);

            // 只有当 secret 不为空时才设置
            if (!empty($endpoint->secret)) {
                $webhookCall->useSecret($endpoint->secret);
            }

            // 使用 HTTP 客户端直接发送测试请求
            $startTime = microtime(true);

            try {
                $client = new \GuzzleHttp\Client([
                    'timeout' => $endpoint->timeout,
                    'verify' => false, // 允许自签名证书
                ]);

                $headers = [
                    'Content-Type' => 'application/json',
                    'X-Webhook-Event' => 'test',
                    'X-Webhook-Source' => 'paas-webhook-test',
                    'User-Agent' => 'PaaS-Webhook/1.0',
                ];

                // 如果有 secret，添加签名
                if (!empty($endpoint->secret)) {
                    $payload = json_encode($testPayload);
                    $signature = hash_hmac('sha256', $payload, $endpoint->secret);
                    $headers['X-Webhook-Signature'] = 'sha256=' . $signature;
                }

                $response = $client->post($endpoint->url, [
                    'headers' => $headers,
                    'json' => $testPayload,
                ]);

                $responseTime = round((microtime(true) - $startTime) * 1000, 2);

                return [
                    'success' => true,
                    'message' => 'Webhook 测试成功',
                    'response_status' => $response->getStatusCode(),
                    'response_body' => $response->getBody()->getContents(),
                    'response_time' => $responseTime . 'ms',
                ];

            } catch (\GuzzleHttp\Exception\RequestException $e) {
                $responseTime = round((microtime(true) - $startTime) * 1000, 2);
                $statusCode = $e->hasResponse() ? $e->getResponse()->getStatusCode() : 0;
                $responseBody = $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : '';

                return [
                    'success' => false,
                    'message' => 'Webhook 测试失败',
                    'response_status' => $statusCode,
                    'response_body' => $responseBody,
                    'response_time' => $responseTime . 'ms',
                    'error' => $e->getMessage(),
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
