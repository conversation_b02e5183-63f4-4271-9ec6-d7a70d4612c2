<?php

namespace App\Console\Commands\Pricing;

use App\Models\ResourcePricing;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;

class DeleteResourcePricing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pricing:delete 
                          {resource_type? : 资源类型}
                          {resource_id? : 资源ID}
                          {unit_type? : 单位类型}
                          {--all : 删除所有定价记录}
                          {--force : 强制删除，跳过确认}
                          {--list : 列出所有可删除的定价}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除资源定价记录';

    /**
     * 资源类型配置缓存
     */
    protected ?array $resourceTypeConfig = null;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // 初始化资源类型配置
            $this->initializeResourceTypeConfig();

            if ($this->option('list')) {
                return $this->listDeletablePricings();
            }

            if ($this->option('all')) {
                return $this->deleteAllPricings();
            }

            $resourceType = $this->argument('resource_type');
            $resourceId = $this->argument('resource_id');
            $unitType = $this->argument('unit_type');

            // 如果没有提供任何参数，显示帮助信息
            if (! $resourceType) {
                return $this->showHelp();
            }

            // 验证资源类型
            if (! $this->isValidResourceType($resourceType)) {
                $this->error("❌ 不支持的资源类型: {$resourceType}");
                $this->showSupportedResourceTypes();

                return 1;
            }

            // 如果只提供了资源类型，列出该类型的所有可删除定价
            if (! $resourceId) {
                return $this->listPricingsByResourceType($resourceType);
            }

            // 验证资源是否存在
            $resource = $this->getResourceModel($resourceType, $resourceId);
            if (! $resource) {
                $this->error("❌ 资源不存在: {$resourceType} ID {$resourceId}");

                return 1;
            }

            // 如果没有提供单位类型，列出该资源的所有可删除定价
            if (! $unitType) {
                return $this->listResourcePricings($resource);
            }

            // 删除指定的定价
            return $this->deleteSpecificPricing($resource, $unitType);

        } catch (\Exception $e) {
            $this->error("❌ 删除定价失败: {$e->getMessage()}");
            $this->line('🔍 Stack trace:');
            $this->line($e->getTraceAsString());

            return 1;
        }
    }

    /**
     * 初始化资源类型配置
     */
    protected function initializeResourceTypeConfig(): void
    {
        if ($this->resourceTypeConfig !== null) {
            return;
        }

        $models = config('pricable.models', []);
        $unitTypes = config('pricable.unit_types', []);

        $this->resourceTypeConfig = [];

        foreach ($models as $modelClass => $resourceType) {
            if (! class_exists($modelClass)) {
                continue;
            }

            $this->resourceTypeConfig[$resourceType] = [
                'model_class' => $modelClass,
                'unit_types' => $unitTypes[$resourceType] ?? [],
                'display_name' => $this->getResourceTypeDisplayName($resourceType),
                'description' => $this->getResourceTypeDescription($resourceType),
            ];
        }
    }

    /**
     * 获取资源类型显示名称
     */
    protected function getResourceTypeDisplayName(string $resourceType): string
    {
        return match ($resourceType) {
            'cluster' => '集群资源',
            'ip' => 'IP池资源',
            'gpu' => 'GPU资源',
            default => ucfirst($resourceType).'资源',
        };
    }

    /**
     * 获取资源类型描述
     */
    protected function getResourceTypeDescription(string $resourceType): string
    {
        return match ($resourceType) {
            'cluster' => '提供计算和存储资源的集群',
            'ip' => '提供外部IP地址的IP池',
            'gpu' => '提供GPU计算资源的GPU池',
            default => $resourceType.'资源',
        };
    }

    /**
     * 显示帮助信息
     */
    protected function showHelp(): int
    {
        $this->info('🗑️  资源定价删除工具');
        $this->newLine();

        $this->info('📖 用法:');
        $this->line('  php artisan pricing:delete                                    # 显示此帮助信息');
        $this->line('  php artisan pricing:delete <资源类型>                          # 列出指定类型的所有可删除定价');
        $this->line('  php artisan pricing:delete <资源类型> <资源ID>                   # 列出指定资源的所有可删除定价');
        $this->line('  php artisan pricing:delete <资源类型> <资源ID> <单位类型>         # 删除指定的定价');
        $this->newLine();

        $this->info('⚙️  选项:');
        $this->line('  --all                  删除所有定价记录');
        $this->line('  --list                 列出所有可删除的定价');
        $this->line('  --force                强制删除，跳过确认');
        $this->newLine();

        $this->info('🔧 支持的资源类型:');
        $this->showSupportedResourceTypes();

        $this->newLine();
        $this->info('💡 示例:');
        $supportedTypes = array_keys($this->resourceTypeConfig);
        if (! empty($supportedTypes)) {
            $firstType = $supportedTypes[0];
            $unitTypes = array_keys($this->resourceTypeConfig[$firstType]['unit_types'] ?? []);
            $firstUnit = $unitTypes[0] ?? 'unit_type';

            $this->line("  php artisan pricing:delete {$firstType}                           # 列出所有{$firstType}定价");
            $this->line("  php artisan pricing:delete {$firstType} 1                         # 列出{$firstType}资源1的所有定价");
            $this->line("  php artisan pricing:delete {$firstType} 1 {$firstUnit}               # 删除{$firstType}资源1的{$firstUnit}定价");
        }
        $this->line('  php artisan pricing:delete --all                             # 删除所有定价记录');

        return 0;
    }

    /**
     * 检查资源类型是否有效
     */
    protected function isValidResourceType(string $resourceType): bool
    {
        return isset($this->resourceTypeConfig[$resourceType]);
    }

    /**
     * 显示支持的资源类型
     */
    protected function showSupportedResourceTypes(): void
    {
        if (empty($this->resourceTypeConfig)) {
            $this->warn('❌ 没有配置任何资源类型');
            $this->info('💡 请在 config/pricable.php 中配置 models 数组');

            return;
        }

        $rows = [];
        foreach ($this->resourceTypeConfig as $resourceType => $config) {
            $rows[] = [
                $resourceType,
                $config['display_name'],
                $config['model_class'],
                count($config['unit_types']),
            ];
        }

        $this->table(
            ['资源类型', '描述', '模型类', '单位类型数量'],
            $rows
        );
    }

    /**
     * 获取资源模型
     */
    protected function getResourceModel(?string $resourceType, ?string $resourceId): ?Model
    {
        if (! $resourceType || ! $resourceId || ! isset($this->resourceTypeConfig[$resourceType])) {
            return null;
        }

        try {
            $modelClass = $this->resourceTypeConfig[$resourceType]['model_class'];

            return $modelClass::find($resourceId);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 列出所有可删除的定价
     */
    protected function listDeletablePricings(): int
    {
        try {
            $this->info('🌍 所有可删除的定价记录:');

            $pricings = ResourcePricing::with('resource')
                ->orderBy('resource_type')
                ->orderBy('resource_id')
                ->orderBy('unit_type')
                ->orderBy('effective_date', 'desc')
                ->get();

            if ($pricings->isEmpty()) {
                $this->warn('暂无任何定价记录');

                return 0;
            }

            $rows = [];
            foreach ($pricings as $pricing) {
                $resourceName = $pricing->resource->name ?? 'N/A';

                $rows[] = [
                    $pricing->id,
                    $this->getResourceTypeFromModelClass($pricing->resource_type),
                    $pricing->resource_id,
                    $resourceName,
                    $pricing->unit_type,
                    formatAmountForCli((string) $pricing->price_per_unit_per_month),
                    $pricing->effective_date->format('Y-m-d H:i:s'),
                    $pricing->isEffective() ? '✅' : '⏳',
                ];
            }

            $this->table(
                ['ID', '资源类型', '资源ID', '名称', '单位类型', '每月价格', '生效日期', '状态'],
                $rows
            );

            $this->newLine();
            $this->info("💡 使用 'php artisan pricing:delete <资源类型> <资源ID> <单位类型>' 删除指定定价");

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 获取定价列表失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 根据资源类型列出定价
     */
    protected function listPricingsByResourceType(string $resourceType): int
    {
        try {
            $config = $this->resourceTypeConfig[$resourceType];
            $modelClass = $config['model_class'];

            $this->info("📋 {$config['display_name']} 的所有定价记录:");

            $pricings = ResourcePricing::where('resource_type', $modelClass)
                ->with('resource')
                ->orderBy('resource_id')
                ->orderBy('unit_type')
                ->get();

            if ($pricings->isEmpty()) {
                $this->warn("暂无 {$config['display_name']} 的定价记录");

                return 0;
            }

            $rows = [];
            foreach ($pricings as $pricing) {
                $resourceName = $pricing->resource->name ?? 'N/A';

                $rows[] = [
                    $pricing->id,
                    $pricing->resource_id,
                    $resourceName,
                    $pricing->unit_type,
                    formatAmountForCli((string) $pricing->price_per_unit_per_month),
                    $pricing->effective_date->format('Y-m-d H:i:s'),
                    $pricing->isEffective() ? '✅' : '⏳',
                ];
            }

            $this->table(
                ['ID', '资源ID', '名称', '单位类型', '每小时价格', '生效日期', '状态'],
                $rows
            );

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 获取 {$resourceType} 定价列表失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 列出指定资源的定价
     */
    protected function listResourcePricings(Model $resource): int
    {
        try {
            $resourceName = $resource->name ?? 'N/A';
            $this->info("🏷️  资源定价记录: {$resource->id} ({$resourceName})");

            $pricings = ResourcePricing::where('resource_type', get_class($resource))
                ->where('resource_id', $resource->id)
                ->orderBy('unit_type')
                ->orderBy('effective_date', 'desc')
                ->get();

            if ($pricings->isEmpty()) {
                $this->warn('此资源暂无定价记录');

                return 0;
            }

            $rows = [];
            foreach ($pricings as $pricing) {
                $rows[] = [
                    $pricing->id,
                    $pricing->unit_type,
                    formatAmountForCli((string) $pricing->price_per_unit_per_month),
                    $pricing->effective_date->format('Y-m-d H:i:s'),
                    $pricing->isEffective() ? '✅' : '⏳',
                    $pricing->description ?: '-',
                ];
            }

            $this->table(
                ['ID', '单位类型', '每月价格', '生效日期', '状态', '说明'],
                $rows
            );

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 获取资源定价失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 删除指定的定价
     */
    protected function deleteSpecificPricing(Model $resource, string $unitType): int
    {
        try {
            $resourceName = $resource->name ?? 'N/A';

            // 查找指定的定价记录
            $pricings = ResourcePricing::where('resource_type', get_class($resource))
                ->where('resource_id', $resource->id)
                ->where('unit_type', $unitType)
                ->orderBy('effective_date', 'desc')
                ->get();

            if ($pricings->isEmpty()) {
                $this->warn("未找到指定的定价记录: {$resource->id} ({$resourceName}) - {$unitType}");

                return 0;
            }

            $this->info('📋 找到以下定价记录:');
            $rows = [];
            foreach ($pricings as $pricing) {
                $rows[] = [
                    $pricing->id,
                    $pricing->unit_type,
                    formatAmountForCli((string) $pricing->price_per_unit_per_month),
                    $pricing->effective_date->format('Y-m-d H:i:s'),
                    $pricing->isEffective() ? '✅' : '⏳',
                    $pricing->description ?: '-',
                ];
            }

            $this->table(
                ['ID', '单位类型', '每月价格', '生效日期', '状态', '说明'],
                $rows
            );

            if (! $this->option('force')) {
                if (! $this->confirm("确定要删除这 {$pricings->count()} 条定价记录吗？此操作不可逆！")) {
                    $this->info('操作已取消');

                    return 0;
                }
            }

            $deletedCount = 0;
            foreach ($pricings as $pricing) {
                $pricing->delete();
                $deletedCount++;
                $this->info("✅ 已删除定价记录 ID: {$pricing->id}");
            }

            $this->info("🎉 成功删除 {$deletedCount} 条定价记录");

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 删除定价失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 删除所有定价
     */
    protected function deleteAllPricings(): int
    {
        try {
            $totalCount = ResourcePricing::count();

            if ($totalCount === 0) {
                $this->warn('暂无任何定价记录');

                return 0;
            }

            $this->warn("⚠️  危险操作：即将删除所有 {$totalCount} 条定价记录！");

            if (! $this->option('force')) {
                if (! $this->confirm('确定要删除所有定价记录吗？此操作不可逆！')) {
                    $this->info('操作已取消');

                    return 0;
                }

                // 二次确认
                if (! $this->confirm('这是危险操作，再次确认删除所有定价记录？')) {
                    $this->info('操作已取消');

                    return 0;
                }
            }

            $deletedCount = ResourcePricing::count();
            ResourcePricing::truncate();

            $this->info("🎉 成功删除所有 {$deletedCount} 条定价记录");

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 删除所有定价失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 根据模型获取资源类型
     */
    protected function getResourceTypeFromModel(Model $model): string
    {
        $modelClass = get_class($model);

        return $this->getResourceTypeFromModelClass($modelClass);
    }

    /**
     * 根据模型类获取资源类型
     */
    protected function getResourceTypeFromModelClass(string $modelClass): string
    {
        $models = config('pricable.models', []);

        return $models[$modelClass] ?? 'unknown';
    }
}
