<?php

namespace App\Console\Commands\Pricing;

use App\Models\ResourcePricing;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;

class SetResourcePricing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pricing:set 
                          {resource_type? : 资源类型}
                          {resource_id? : 资源ID}
                          {unit_type? : 单位类型}
                          {price? : 价格（默认为每月价格）}
                          {--hourly : 价格单位为每小时（默认为每月）}
                          {--description= : 定价说明}
                          {--effective-date= : 生效日期 (Y-m-d H:i:s 格式，默认立即生效)}
                          {--list : 列出资源的当前定价}
                          {--list-all : 列出所有定价}
                          {--list-resources : 列出所有可设置定价的资源}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '设置资源定价（支持集群、IP池等）- 默认输入月价格，自动计算其他时间单位';

    /**
     * 资源类型配置缓存
     */
    protected ?array $resourceTypeConfig = null;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // 初始化资源类型配置
            $this->initializeResourceTypeConfig();

            // 处理列表选项
            if ($this->option('list-all')) {
                return $this->listAllPricings();
            }

            if ($this->option('list-resources')) {
                return $this->listAllResources();
            }

            $resourceType = $this->argument('resource_type');
            $resourceId = $this->argument('resource_id');

            // 如果没有提供任何参数，显示帮助信息
            if (! $resourceType || trim($resourceType) === '') {
                return $this->showHelp();
            }

            // 验证资源类型
            if (! $this->isValidResourceType($resourceType)) {
                $this->error("❌ 不支持的资源类型: {$resourceType}");
                $this->showSupportedResourceTypes();

                return 1;
            }

            // 如果只提供了资源类型，列出该类型的所有资源
            if (! $resourceId) {
                return $this->listResourcesByType($resourceType);
            }

            // 获取资源模型
            $resource = $this->getResourceModel($resourceType, $resourceId);
            if (! $resource) {
                $this->error("❌ 资源不存在: {$resourceType} ID {$resourceId}");
                $this->info('💡 使用 --list-resources 查看所有可用资源');

                return 1;
            }

            if ($this->option('list')) {
                return $this->listResourcePricing($resource);
            }

            $unitType = $this->argument('unit_type');
            $price = $this->argument('price');

            // 如果没有提供单位类型，显示支持的单位类型
            if (! $unitType) {
                $this->info("📋 资源 {$resourceType} ID {$resourceId} 支持的单位类型:");
                $this->showSupportedUnitTypes($resourceType);

                return 0;
            }

            // 如果没有提供价格，提示用户
            if ($price === null) {
                $this->error('❌ 请提供价格');
                $priceUnit = $this->option('hourly') ? '每小时' : '每月';
                $this->info("💡 用法: php artisan pricing:set {$resourceType} {$resourceId} {$unitType} <{$priceUnit}价格>");
                $this->info("💡 示例: php artisan pricing:set {$resourceType} {$resourceId} {$unitType} 30.0  # 月价格30元");
                $this->info("💡 示例: php artisan pricing:set {$resourceType} {$resourceId} {$unitType} 0.05 --hourly  # 小时价格0.05元");

                return 1;
            }

            // 验证单位类型
            if (! $this->validateUnitType($resourceType, $unitType)) {
                $this->error("❌ 不支持的单位类型: {$unitType} 对于资源类型: {$resourceType}");
                $this->showSupportedUnitTypes($resourceType);

                return 1;
            }

            // 验证价格
            if (! is_numeric($price) || $price < 0) {
                $this->error("❌ 价格必须是非负数: {$price}");

                return 1;
            }

            // 根据输入单位转换为每月价格
            $isHourlyInput = $this->option('hourly');
            if ($isHourlyInput) {
                // 如果输入的是每小时价格，转换为每月价格
                $pricePerHour = $price;
                $pricePerMonth = $this->calculateMonthlyPrice($pricePerHour);
                $inputUnit = '每小时';
            } else {
                // 如果输入的是每月价格（默认），直接使用
                $pricePerMonth = $price;
                $pricePerHour = $this->calculateHourlyPrice($pricePerMonth);
                $inputUnit = '每月';
            }

            // 显示价格转换信息
            $this->info("💰 价格转换信息 (输入单位: {$inputUnit}):");
            $this->table(
                ['时间单位', '价格'],
                [
                    ['每月', formatAmountForCli($pricePerMonth)],
                    ['每日', formatAmountForCli($this->calculateDailyPrice($pricePerMonth))],
                    ['每小时', formatAmountForCli($pricePerHour)],
                    ['每分钟', formatAmountForCli($this->calculateMinutePriceFromMonth($pricePerMonth))],
                ]
            );
            $this->newLine();

            // 将科学计数法转换为标准格式
            $normalizedPrice = number_format((float) $pricePerMonth, 8, '.', '');

            // 验证价格范围（数据库字段为 decimal(12,8)，最大值应该小于 10^4）
            $maxPrice = 9999.99999999; // 最大安全价格
            if (bccomp($normalizedPrice, (string) $maxPrice, 8) > 0) {
                $this->error("❌ 价格超出允许范围，最大值为 {$maxPrice}: {$pricePerMonth}");
                $this->info('💡 数据库字段精度限制为 decimal(12,8)');

                return 1;
            }

            // 验证小数位数不超过8位（使用标准化后的价格）
            $decimalParts = explode('.', $normalizedPrice);
            if (isset($decimalParts[1]) && strlen(rtrim($decimalParts[1], '0')) > 8) {
                $this->error("❌ 价格小数位数不能超过8位: {$pricePerMonth}");
                $this->info('💡 当前有效小数位数: '.strlen(rtrim($decimalParts[1], '0')));

                return 1;
            }

            // 使用标准化后的价格进行后续处理
            $pricePerMonth = $normalizedPrice;

            $description = $this->option('description');
            $effectiveDate = $this->option('effective-date');

            // 解析生效日期
            try {
                $effectiveDateParsed = $effectiveDate ? \Carbon\Carbon::parse($effectiveDate) : now();
            } catch (\Exception $e) {
                $this->error("❌ 生效日期格式错误: {$effectiveDate}");
                $this->info('💡 正确格式: Y-m-d H:i:s (例如: 2024-01-15 10:30:00)');

                return 1;
            }

            // 创建定价记录
            $pricing = ResourcePricing::createForResource(
                $resource,
                $unitType,
                $pricePerMonth,
                $description,
                $effectiveDateParsed
            );

            $this->info('✅ 定价设置成功!');
            $this->showPricingDetails($resource, $pricing);

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 设置定价失败: {$e->getMessage()}");
            $this->line('🔍 Stack trace:');
            $this->line($e->getTraceAsString());

            return 1;
        }
    }

    /**
     * 初始化资源类型配置
     */
    protected function initializeResourceTypeConfig(): void
    {
        if ($this->resourceTypeConfig !== null) {
            return;
        }

        $models = config('pricable.models', []);
        $unitTypes = config('pricable.unit_types', []);

        $this->resourceTypeConfig = [];

        foreach ($models as $modelClass => $resourceType) {
            if (! class_exists($modelClass)) {
                continue;
            }

            $this->resourceTypeConfig[$resourceType] = [
                'model_class' => $modelClass,
                'unit_types' => $unitTypes[$resourceType] ?? [],
                'display_name' => $this->getResourceTypeDisplayName($resourceType),
                'description' => $this->getResourceTypeDescription($resourceType),
            ];
        }
    }

    /**
     * 获取资源类型显示名称
     */
    protected function getResourceTypeDisplayName(string $resourceType): string
    {
        return match ($resourceType) {
            'cluster' => '集群资源',
            'ip' => 'IP池资源',
            'gpu' => 'GPU资源',
            default => ucfirst($resourceType).'资源',
        };
    }

    /**
     * 获取资源类型描述
     */
    protected function getResourceTypeDescription(string $resourceType): string
    {
        return match ($resourceType) {
            'cluster' => '提供计算和存储资源的集群',
            'ip' => '提供外部IP地址的IP池',
            'gpu' => '提供GPU计算资源的GPU池',
            default => $resourceType.'资源',
        };
    }

    /**
     * 显示帮助信息
     */
    protected function showHelp(): int
    {
        $this->info('🏷️  资源定价管理工具');
        $this->newLine();

        $this->info('📖 用法:');
        $this->line('  php artisan pricing:set                           # 显示此帮助信息');
        $this->line('  php artisan pricing:set <资源类型>                  # 列出指定类型的所有资源');
        $this->line('  php artisan pricing:set <资源类型> <资源ID>           # 显示资源支持的单位类型');
        $this->line('  php artisan pricing:set <资源类型> <资源ID> <单位类型> <月价格> # 设置定价（默认月价格）');
        $this->line('  php artisan pricing:set <资源类型> <资源ID> <单位类型> <小时价格> --hourly # 设置定价（小时价格）');
        $this->newLine();

        $this->info('⚙️  选项:');
        $this->line('  --hourly                使用小时价格而非月价格（默认）');
        $this->line('  --list-all              列出所有定价');
        $this->line('  --list-resources        列出所有可设置定价的资源');
        $this->line('  --list                  列出指定资源的当前定价');
        $this->line('  --description=<说明>     添加定价说明');
        $this->line('  --effective-date=<日期>  设置生效日期');
        $this->newLine();

        $this->info('🔧 支持的资源类型:');
        $this->showSupportedResourceTypes();

        $this->newLine();
        $this->info('💡 示例:');
        $supportedTypes = array_keys($this->resourceTypeConfig);
        if (! empty($supportedTypes)) {
            $firstType = $supportedTypes[0];
            $unitTypes = array_keys($this->resourceTypeConfig[$firstType]['unit_types'] ?? []);
            $firstUnit = $unitTypes[0] ?? 'unit_type';

            $this->line("  php artisan pricing:set {$firstType}                   # 列出所有{$firstType}资源");
            $this->line("  php artisan pricing:set {$firstType} 1                 # 显示{$firstType}资源1支持的单位类型");
            $this->line("  php artisan pricing:set {$firstType} 1 {$firstUnit} 30.0  # 设置月价格30元");
            $this->line("  php artisan pricing:set {$firstType} 1 {$firstUnit} 0.05 --hourly  # 设置小时价格0.05元");
        }

        $this->newLine();
        $this->info('📊 价格转换说明:');
        $this->line('  • 默认输入月价格，自动计算其他时间单位');
        $this->line('  • 月价格 ÷ 720 = 小时价格（按30天计算）');
        $this->line('  • 小时价格 ÷ 60 = 分钟价格');
        $this->line('  • 月价格 ÷ 30 = 日价格');
        $this->line('  • 支持8位小数精度，适合微小价格设置');

        return 0;
    }

    /**
     * 检查资源类型是否有效
     */
    protected function isValidResourceType(string $resourceType): bool
    {
        return isset($this->resourceTypeConfig[$resourceType]);
    }

    /**
     * 显示支持的资源类型
     */
    protected function showSupportedResourceTypes(): void
    {
        if (empty($this->resourceTypeConfig)) {
            $this->warn('❌ 没有配置任何资源类型');
            $this->info('💡 请在 config/pricable.php 中配置 models 数组');

            return;
        }

        $rows = [];
        foreach ($this->resourceTypeConfig as $resourceType => $config) {
            $rows[] = [
                $resourceType,
                $config['display_name'],
                $config['model_class'],
                count($config['unit_types']),
            ];
        }

        $this->table(
            ['资源类型', '描述', '模型类', '单位类型数量'],
            $rows
        );
    }

    /**
     * 根据类型列出所有资源
     */
    protected function listResourcesByType(string $resourceType): int
    {
        try {
            $config = $this->resourceTypeConfig[$resourceType];
            $modelClass = $config['model_class'];

            $this->info("📋 {$config['display_name']} 的所有资源:");

            if (! class_exists($modelClass)) {
                $this->error("❌ 模型类不存在: {$modelClass}");

                return 1;
            }

            $resources = $this->getResourcesForModel($modelClass);

            if ($resources->isEmpty()) {
                $this->warn("暂无 {$config['display_name']} 资源");

                return 0;
            }

            $this->displayResourceTable($resources, $resourceType);

            $this->newLine();
            $this->info("💡 使用 'php artisan pricing:set {$resourceType} <ID>' 查看资源支持的单位类型");

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 获取 {$resourceType} 资源列表失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 获取模型的资源列表
     */
    protected function getResourcesForModel(string $modelClass)
    {
        $model = new $modelClass;

        // 动态确定要选择的字段
        $selectFields = ['id'];

        // 检查模型是否有 name 字段
        if ($model->getConnection()->getSchemaBuilder()->hasColumn($model->getTable(), 'name')) {
            $selectFields[] = 'name';
        }

        // 根据模型类型添加特定字段
        $additionalFields = $this->getAdditionalFieldsForModel($modelClass);
        $selectFields = array_merge($selectFields, $additionalFields);

        return $modelClass::select($selectFields)->get();
    }

    /**
     * 获取模型的额外字段
     */
    protected function getAdditionalFieldsForModel(string $modelClass): array
    {
        return match ($modelClass) {
            \App\Models\Cluster::class => ['server_url'],
            \App\Models\IpPool::class => ['driver', 'is_active'],
            default => [],
        };
    }

    /**
     * 显示资源表格
     */
    protected function displayResourceTable($resources, string $resourceType): void
    {
        $rows = [];
        $headers = ['ID', '名称'];

        foreach ($resources as $resource) {
            $row = [
                $resource->id,
                $resource->name ?? 'N/A',
            ];

            // 根据资源类型添加额外列
            if ($resourceType === 'cluster' && isset($resource->server_url)) {
                if (count($headers) === 2) {
                    $headers[] = '服务器URL';
                    $headers[] = '工作空间数';
                }
                $row[] = $resource->server_url ?? 'N/A';
                $workspaceCount = method_exists($resource, 'workspaces') ? $resource->workspaces()->count() : 0;
                $row[] = $workspaceCount;
            } elseif ($resourceType === 'ip' && isset($resource->driver)) {
                if (count($headers) === 2) {
                    $headers[] = '驱动';
                    $headers[] = '状态';
                }
                $row[] = $resource->driver ?? 'N/A';
                $row[] = ($resource->is_active ?? false) ? '✅' : '❌';
            }

            $rows[] = $row;
        }

        $this->table($headers, $rows);
    }

    /**
     * 列出所有可设置定价的资源
     */
    protected function listAllResources(): int
    {
        $this->info('🌍 所有可设置定价的资源:');
        $this->newLine();

        try {
            foreach ($this->resourceTypeConfig as $resourceType => $config) {
                $modelClass = $config['model_class'];

                $this->info("🔧 {$config['display_name']} ({$resourceType}):");

                if (! class_exists($modelClass)) {
                    $this->warn("  ❌ 模型类不存在: {$modelClass}");

                    continue;
                }

                $resources = $this->getResourcesForModel($modelClass);

                if ($resources->isEmpty()) {
                    $this->warn("  暂无{$config['display_name']}");
                } else {
                    $this->displayResourceTable($resources, $resourceType);
                }

                $this->newLine();
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 获取资源列表失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 获取资源模型
     */
    protected function getResourceModel(?string $resourceType, ?string $resourceId): ?Model
    {
        if (! $resourceType || ! $resourceId || ! isset($this->resourceTypeConfig[$resourceType])) {
            return null;
        }

        try {
            $modelClass = $this->resourceTypeConfig[$resourceType]['model_class'];

            return $modelClass::find($resourceId);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 验证单位类型
     */
    protected function validateUnitType(string $resourceType, string $unitType): bool
    {
        $config = $this->resourceTypeConfig[$resourceType] ?? [];
        $unitTypes = $config['unit_types'] ?? [];

        return isset($unitTypes[$unitType]);
    }

    /**
     * 显示支持的单位类型
     */
    protected function showSupportedUnitTypes(string $resourceType): void
    {
        $config = $this->resourceTypeConfig[$resourceType] ?? [];
        $unitTypes = $config['unit_types'] ?? [];

        if (empty($unitTypes)) {
            $this->warn("❌ 资源类型 {$resourceType} 没有配置支持的单位类型");

            return;
        }

        $rows = [];
        foreach ($unitTypes as $unitType => $info) {
            $rows[] = [
                $unitType,
                $info['name'] ?? $unitType,
                $info['description'] ?? '无描述',
            ];
        }

        $this->table(['单位类型', '显示名称', '描述'], $rows);
    }

    /**
     * 显示定价详情
     */
    protected function showPricingDetails(Model $resource, ResourcePricing $pricing): void
    {
        $this->newLine();
        $this->info('📊 定价详情:');

        $resourceName = $resource->name ?? 'N/A';

        // 计算各种时间单位的价格
        $monthlyPrice = $pricing->price_per_unit_per_month;
        $dailyPrice = $pricing->price_per_day;
        $hourlyPrice = $pricing->price_per_hour;
        $minutePrice = $pricing->price_per_minute;

        $this->table(
            ['项目', '值'],
            [
                ['资源类型', $pricing->resource_type],
                ['资源ID', $pricing->resource_id],
                ['资源名称', $resourceName],
                ['单位类型', $pricing->unit_type],
                ['单位类型名称', $pricing->getUnitTypeDisplayName()],
                ['生效日期', $pricing->effective_date->format('Y-m-d H:i:s')],
                ['说明', $pricing->description ?: '无'],
            ]
        );

        $this->newLine();
        $this->info('💰 各时间单位价格:');
        $this->table(
            ['时间单位', '价格'],
            [
                ['每月', formatAmountForCli((string) $monthlyPrice)],
                ['每日', formatAmountForCli($dailyPrice)],
                ['每小时', formatAmountForCli($hourlyPrice)],
                ['每分钟', formatAmountForCli($minutePrice)],
            ]
        );
    }

    /**
     * 列出资源的当前定价
     */
    protected function listResourcePricing(Model $resource): int
    {
        try {
            $resourceName = $resource->name ?? 'N/A';
            $this->info("🏷️  资源定价列表: {$resource->id} ({$resourceName})");

            $pricings = ResourcePricing::getAllCurrentPricingForResource($resource);

            if (empty($pricings)) {
                $this->warn('此资源暂无定价配置');
                $resourceType = $this->getResourceTypeFromModel($resource);
                $this->info("💡 使用 'php artisan pricing:set {$resourceType} {$resource->id} <单位类型> <价格>' 设置定价");

                return 0;
            }

            $rows = [];
            foreach ($pricings as $unitType => $pricing) {
                $monthlyPrice = $pricing->price_per_unit_per_month;
                $dailyPrice = $pricing->price_per_day;
                $hourlyPrice = $pricing->price_per_hour;
                $minutePrice = $pricing->price_per_minute;

                $rows[] = [
                    $unitType,
                    $pricing->getUnitTypeDisplayName(),
                    formatAmountForCli((string) $monthlyPrice),
                    formatAmountForCli($dailyPrice),
                    formatAmountForCli($hourlyPrice),
                    formatAmountForCli($minutePrice),
                    $pricing->effective_date->format('Y-m-d H:i:s'),
                    $pricing->description ?: '-',
                ];
            }

            $this->table(
                ['单位类型', '显示名称', '月价格', '日价格', '小时价格', '分钟价格', '生效日期', '说明'],
                $rows
            );

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 获取资源定价失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 列出所有定价
     */
    protected function listAllPricings(): int
    {
        try {
            $this->info('🌍 所有资源定价列表');

            $pricings = ResourcePricing::with('resource')
                ->where('effective_date', '<=', now())
                ->orderBy('resource_type')
                ->orderBy('resource_id')
                ->orderBy('unit_type')
                ->get();

            if ($pricings->isEmpty()) {
                $this->warn('暂无任何定价配置');
                $this->info("💡 使用 'php artisan pricing:set --list-resources' 查看可设置定价的资源");

                return 0;
            }

            $rows = [];
            foreach ($pricings as $pricing) {
                $resourceName = $pricing->resource->name ?? 'N/A';
                $monthlyPrice = $pricing->price_per_unit_per_month;
                $dailyPrice = $pricing->price_per_day;
                $hourlyPrice = $pricing->price_per_hour;
                $minutePrice = $pricing->price_per_minute;

                $rows[] = [
                    $this->getResourceTypeFromModelClass($pricing->resource_type),
                    $pricing->resource_id,
                    $resourceName,
                    $pricing->unit_type,
                    formatAmountForCli((string) $monthlyPrice),
                    formatAmountForCli($dailyPrice),
                    formatAmountForCli($hourlyPrice),
                    formatAmountForCli($minutePrice),
                    $pricing->effective_date->format('Y-m-d H:i:s'),
                ];
            }

            $this->table(
                ['资源类型', 'ID', '名称', '单位类型', '月价格', '日价格', '小时价格', '分钟价格', '生效日期'],
                $rows
            );

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 获取定价列表失败: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * 根据模型获取资源类型
     */
    protected function getResourceTypeFromModel(Model $model): string
    {
        $modelClass = get_class($model);

        return $this->getResourceTypeFromModelClass($modelClass);
    }

    /**
     * 根据模型类获取资源类型
     */
    protected function getResourceTypeFromModelClass(string $modelClass): string
    {
        $models = config('pricable.models', []);

        return $models[$modelClass] ?? 'unknown';
    }

    /**
     * 计算每小时价格（从月价格）
     * 月价格 ÷ (30天 × 24小时) = 小时价格
     */
    protected function calculateHourlyPrice(string $monthlyPrice): string
    {
        // 一个月按30天计算，总共720小时
        return bcdiv($monthlyPrice, '720', 8);
    }

    /**
     * 计算每月价格（从小时价格）
     * 小时价格 × (30天 × 24小时) = 月价格
     */
    protected function calculateMonthlyPrice(string $hourlyPrice): string
    {
        // 一个月按30天计算，总共720小时
        return bcmul($hourlyPrice, '720', 8);
    }

    /**
     * 计算每日价格（从月价格）
     * 月价格 ÷ 30天 = 日价格
     */
    protected function calculateDailyPrice(string $monthlyPrice): string
    {
        return bcdiv($monthlyPrice, '30', 8);
    }

    /**
     * 计算每分钟价格（从小时价格）
     * 小时价格 ÷ 60分钟 = 分钟价格
     */
    protected function calculateMinutePrice(string $hourlyPrice): string
    {
        return bcdiv($hourlyPrice, '60', 8);
    }

    /**
     * 计算每分钟价格（从月价格）
     * 月价格 ÷ 当月天数 ÷ 24小时 ÷ 60分钟 = 分钟价格
     */
    protected function calculateMinutePriceFromMonth(string $monthlyPrice): string
    {
        $daysInMonth = now()->daysInMonth;
        $minutesInMonth = $daysInMonth * 24 * 60;

        return bcdiv($monthlyPrice, (string) $minutesInMonth, 8);
    }
}
