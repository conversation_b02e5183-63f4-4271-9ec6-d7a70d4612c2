<?php

namespace App\Observers;

use App\Events\ClusterPricingUpdated;
use App\Models\Cluster;
use App\Models\ResourcePricing;
use Illuminate\Support\Facades\Log;

class ResourcePricingObserver
{
    /**
     * Handle the ResourcePricing "created" event.
     */
    public function created(ResourcePricing $pricing): void
    {
        Log::info('创建资源定价记录', [
            'resource_type' => $pricing->resource_type,
            'resource_id' => $pricing->resource_id,
            'unit_type' => $pricing->unit_type,
            'price_per_unit_per_month' => $pricing->price_per_unit_per_month,
            'effective_date' => $pricing->effective_date,
        ]);

        // 如果是集群定价创建，触发价格更新事件
        if ($pricing->resource_type === Cluster::class) {
            $this->broadcastClusterPricingEvent($pricing, 'created');
        }
    }

    /**
     * Handle the ResourcePricing "updated" event.
     */
    public function updated(ResourcePricing $pricing): void
    {
        // 检查是否有价格字段变更
        $priceFields = [
            'price_per_unit_per_month',
            'effective_date',
            'description',
        ];

        $changes = [];
        $hasChanges = false;

        foreach ($priceFields as $field) {
            if ($pricing->wasChanged($field)) {
                $hasChanges = true;
                $changes[$field] = [
                    'old' => $pricing->getOriginal($field),
                    'new' => $pricing->getAttribute($field),
                ];
            }
        }

        if ($hasChanges) {
            Log::info('更新资源定价记录', [
                'resource_type' => $pricing->resource_type,
                'resource_id' => $pricing->resource_id,
                'unit_type' => $pricing->unit_type,
                'changes' => $changes,
            ]);

            // 如果价格变更且生效日期是当前时间，可以触发相关业务逻辑
            if (isset($changes['price_per_unit_per_month']) &&
                $pricing->effective_date->lte(now())) {
                $this->handlePriceChange($pricing, $changes);
            }

            // 如果是集群定价更新，触发价格更新事件
            if ($pricing->resource_type === Cluster::class) {
                $this->broadcastClusterPricingEvent($pricing, 'updated', $changes);
            }
        }
    }

    /**
     * Handle the ResourcePricing "deleted" event.
     */
    public function deleted(ResourcePricing $pricing): void
    {
        Log::warning('删除资源定价记录', [
            'resource_type' => $pricing->resource_type,
            'resource_id' => $pricing->resource_id,
            'unit_type' => $pricing->unit_type,
            'price_per_unit_per_month' => $pricing->price_per_unit_per_month,
        ]);

        // 如果是集群定价删除，触发价格更新事件
        if ($pricing->resource_type === Cluster::class) {
            $this->broadcastClusterPricingEvent($pricing, 'deleted');
        }
    }

    /**
     * 处理价格变更
     */
    protected function handlePriceChange(ResourcePricing $pricing, array $changes): void
    {
        // 在这里可以添加价格变更的业务逻辑
        // 例如：
        // 1. 发送价格变更通知
        // 2. 记录价格历史
        // 3. 触发相关事件

        Log::info('价格变更生效', [
            'resource_type' => $pricing->resource_type,
            'resource_id' => $pricing->resource_id,
            'unit_type' => $pricing->unit_type,
            'old_price' => $changes['price_per_unit_per_month']['old'] ?? null,
            'new_price' => $changes['price_per_unit_per_month']['new'] ?? null,
        ]);
    }

    /**
     * 广播集群定价更新事件
     */
    protected function broadcastClusterPricingEvent(ResourcePricing $pricing, string $action, array $changes = []): void
    {
        try {
            $cluster = Cluster::find($pricing->resource_id);

            if ($cluster) {
                ClusterPricingUpdated::dispatch($cluster, $pricing, $changes);

                Log::debug('集群定价更新事件已触发', [
                    'cluster_id' => $cluster->id,
                    'cluster_name' => $cluster->name,
                    'unit_type' => $pricing->unit_type,
                    'action' => $action,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('广播集群定价更新事件失败', [
                'resource_type' => $pricing->resource_type,
                'resource_id' => $pricing->resource_id,
                'unit_type' => $pricing->unit_type,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
