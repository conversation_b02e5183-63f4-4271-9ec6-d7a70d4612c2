<?php

namespace App\Listeners;

use App\Events\K8s\BaseK8sEvent;
use App\Events\K8s\K8sEventFactory;
use App\Events\ResourceChanged;
use App\Models\WebhookEndpoint;
use App\Service\WebhookService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ResourceChangedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected WebhookService $webhookService;

    /**
     * Create the event listener.
     */
    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Handle the event.
     */
    public function handle(ResourceChanged $event): void
    {
        try {
            // 获取该命名空间对应的工作空间的所有活跃 webhook 端点
            $webhookEndpoints = WebhookEndpoint::whereHas('workspace', function ($query) use ($event) {
                $query->where('namespace', $event->namespace)
                      ->where('cluster_id', $event->clusterId);
            })
            ->where('is_active', true)
            ->get();

            if ($webhookEndpoints->isEmpty()) {
                return;
            }

            // 使用新的事件工厂创建细化的事件
            $k8sEvents = K8sEventFactory::createFromResourceChanges(
                $event->namespace,
                $event->clusterName,
                $event->clusterId,
                $event->resourceType,
                $event->changes,
                [
                    'summary' => $event->summary,
                    'original_event_id' => uniqid('resource_changed_', true),
                ]
            );

            // 处理每个细化的事件
            foreach ($k8sEvents as $k8sEvent) {
                $this->processK8sEvent($webhookEndpoints, $k8sEvent);
            }

            // 如果没有细化的事件，回退到原始处理方式
            if (empty($k8sEvents)) {
                $this->processLegacyResourceChanged($webhookEndpoints, $event);
            }

        } catch (\Exception $e) {
            Log::error('Webhook 事件处理失败', [
                'event' => [
                    'namespace' => $event->namespace,
                    'cluster_id' => $event->clusterId,
                    'resource_type' => $event->resourceType,
                ],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * 处理细化的 K8s 事件
     */
    protected function processK8sEvent($webhookEndpoints, BaseK8sEvent $k8sEvent): void
    {
        foreach ($webhookEndpoints as $endpoint) {
            // 使用新的事件匹配逻辑
            if (!$k8sEvent->matchesWebhookFilters(
                $endpoint->resource_types ?? [],
                $endpoint->event_types ?? []
            )) {
                continue;
            }

            // 发送 webhook
            $this->webhookService->sendK8sEventWebhook($endpoint, $k8sEvent);
        }
    }

    /**
     * 处理传统的资源变化事件（回退方案）
     */
    protected function processLegacyResourceChanged($webhookEndpoints, ResourceChanged $event): void
    {
        // 为每个变化类型处理 webhook
        foreach (['created', 'updated', 'deleted'] as $changeType) {
            if (empty($event->changes[$changeType])) {
                continue;
            }

            foreach ($event->changes[$changeType] as $resourceData) {
                $this->processResourceChange(
                    $webhookEndpoints,
                    $changeType,
                    $event->resourceType,
                    $resourceData,
                    $event
                );
            }
        }
    }

    /**
     * 处理单个资源变化（传统方式）
     */
    protected function processResourceChange(
        $webhookEndpoints,
        string $changeType,
        string $resourceType,
        array $resourceData,
        ResourceChanged $event
    ): void {
        foreach ($webhookEndpoints as $endpoint) {
            // 检查是否监听该资源类型和事件类型
            if (!$endpoint->listensToResourceType($resourceType) ||
                !$endpoint->listensToEventType($changeType)) {
                continue;
            }

            // 发送 webhook
            $this->webhookService->sendWebhook(
                $endpoint,
                $changeType,
                $resourceType,
                $resourceData,
                $event
            );
        }
    }
}
