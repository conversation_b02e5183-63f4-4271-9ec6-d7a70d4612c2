<?php

namespace App\Listeners;

use App\Events\K8s\K8sEventFactory;
use App\Events\ResourceChanged;
use App\Models\WebhookEndpoint;
use App\Models\Workspace;
use App\Service\WebhookService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ResourceChangedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected WebhookService $webhookService;

    /**
     * Create the event listener.
     */
    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Handle the event.
     */
    public function handle(ResourceChanged $event): void
    {
        try {
            // 优化查询：使用缓存和单次查询获取所有相关端点
            $endpoints = $this->getActiveWebhookEndpoints($event->namespace, $event->clusterId);

            if ($endpoints->isEmpty()) {
                return;
            }

            // 分组端点以优化处理
            $allEventEndpoints = $endpoints->where('listen_all_events', true);
            $specificEventEndpoints = $endpoints->where('listen_all_events', false);

            // 批量创建事件（减少内存使用）
            $this->processResourceChanges($event, $allEventEndpoints, $specificEventEndpoints);

        } catch (\Exception $e) {
            Log::error('Webhook 事件处理失败', [
                'event' => [
                    'namespace' => $event->namespace,
                    'cluster_id' => $event->clusterId,
                    'resource_type' => $event->resourceType,
                ],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * 获取活跃的 webhook 端点（带缓存优化）
     */
    protected function getActiveWebhookEndpoints(string $namespace, int $clusterId)
    {
        $cacheKey = "webhook_endpoints:{$namespace}:{$clusterId}";

        return Cache::remember($cacheKey, 300, function () use ($namespace, $clusterId) {
            // 优化查询：使用 join 替代 whereHas，减少子查询
            return WebhookEndpoint::join('workspaces', 'webhook_endpoints.workspace_id', '=', 'workspaces.id')
                ->where('workspaces.namespace', $namespace)
                ->where('workspaces.cluster_id', $clusterId)
                ->where('webhook_endpoints.is_active', true)
                ->select('webhook_endpoints.*')
                ->get();
        });
    }

    /**
     * 批量处理资源变化（优化内存使用）
     */
    protected function processResourceChanges(
        ResourceChanged $event,
        $allEventEndpoints,
        $specificEventEndpoints
    ): void {
        // 按变化类型分批处理，避免一次性创建所有事件对象
        foreach (['created', 'updated', 'deleted'] as $changeType) {
            if (empty($event->changes[$changeType])) {
                continue;
            }

            $this->processChangeType(
                $event,
                $changeType,
                $event->changes[$changeType],
                $allEventEndpoints,
                $specificEventEndpoints
            );
        }
    }

    /**
     * 处理特定类型的变化
     */
    protected function processChangeType(
        ResourceChanged $event,
        string $changeType,
        array $resources,
        $allEventEndpoints,
        $specificEventEndpoints
    ): void {
        // 分批处理资源，避免内存峰值
        $batchSize = 10;
        $resourceBatches = array_chunk($resources, $batchSize);

        foreach ($resourceBatches as $batch) {
            $this->processBatch(
                $event,
                $changeType,
                $batch,
                $allEventEndpoints,
                $specificEventEndpoints
            );

            // 强制垃圾回收，释放内存
            if (memory_get_usage() > 50 * 1024 * 1024) { // 50MB
                gc_collect_cycles();
            }
        }
    }

    /**
     * 处理资源批次
     */
    protected function processBatch(
        ResourceChanged $event,
        string $changeType,
        array $batch,
        $allEventEndpoints,
        $specificEventEndpoints
    ): void {
        foreach ($batch as $resourceData) {
            // 创建单个事件（减少内存使用）
            $k8sEvents = K8sEventFactory::createFromResourceChanges(
                $event->namespace,
                $event->clusterName,
                $event->clusterId,
                $event->resourceType,
                [$changeType => [$resourceData]], // 只处理当前资源
                [
                    'summary' => $event->summary,
                    'original_event_id' => uniqid('resource_changed_', true),
                ]
            );

            foreach ($k8sEvents as $k8sEvent) {
                // 批量发送给监听所有事件的端点
                $this->sendToAllEventEndpoints($allEventEndpoints, $k8sEvent);

                // 发送给监听特定事件的端点
                $this->sendToSpecificEventEndpoints($specificEventEndpoints, $k8sEvent);

                // 释放事件对象内存
                unset($k8sEvent);
            }

            unset($k8sEvents);
        }
    }

    /**
     * 发送给监听所有事件的端点
     */
    protected function sendToAllEventEndpoints($endpoints, $k8sEvent): void
    {
        foreach ($endpoints as $endpoint) {
            $this->webhookService->sendK8sEventWebhook($endpoint, $k8sEvent);
        }
    }

    /**
     * 发送给监听特定事件的端点
     */
    protected function sendToSpecificEventEndpoints($endpoints, $k8sEvent): void
    {
        $eventType = $k8sEvent->getWebhookEventType();

        foreach ($endpoints as $endpoint) {
            if ($endpoint->listensToEventType($eventType)) {
                $this->webhookService->sendK8sEventWebhook($endpoint, $k8sEvent);
            }
        }
    }


}
