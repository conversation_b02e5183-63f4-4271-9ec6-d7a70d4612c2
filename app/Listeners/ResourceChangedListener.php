<?php

namespace App\Listeners;

use App\Events\K8s\K8sEventFactory;
use App\Events\ResourceChanged;
use App\Models\WebhookEndpoint;
use App\Service\WebhookService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ResourceChangedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected WebhookService $webhookService;

    /**
     * Create the event listener.
     */
    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Handle the event.
     */
    public function handle(ResourceChanged $event): void
    {
        try {
            // 优化查询：分别获取监听所有事件和特定事件的端点
            $allEventEndpoints = WebhookEndpoint::whereHas('workspace', function ($query) use ($event) {
                $query->where('namespace', $event->namespace)
                      ->where('cluster_id', $event->clusterId);
            })
            ->where('is_active', true)
            ->where('listen_all_events', true)
            ->get();

            $specificEventEndpoints = WebhookEndpoint::whereHas('workspace', function ($query) use ($event) {
                $query->where('namespace', $event->namespace)
                      ->where('cluster_id', $event->clusterId);
            })
            ->where('is_active', true)
            ->where('listen_all_events', false)
            ->get();

            if ($allEventEndpoints->isEmpty() && $specificEventEndpoints->isEmpty()) {
                return;
            }

            // 使用新的事件工厂创建细化的事件
            $k8sEvents = K8sEventFactory::createFromResourceChanges(
                $event->namespace,
                $event->clusterName,
                $event->clusterId,
                $event->resourceType,
                $event->changes,
                [
                    'summary' => $event->summary,
                    'original_event_id' => uniqid('resource_changed_', true),
                ]
            );

            // 处理每个细化的事件
            foreach ($k8sEvents as $k8sEvent) {
                // 监听所有事件的端点直接发送
                foreach ($allEventEndpoints as $endpoint) {
                    $this->webhookService->sendK8sEventWebhook($endpoint, $k8sEvent);
                }

                // 监听特定事件的端点需要检查匹配
                foreach ($specificEventEndpoints as $endpoint) {
                    if ($endpoint->listensToEventType($k8sEvent->getWebhookEventType())) {
                        $this->webhookService->sendK8sEventWebhook($endpoint, $k8sEvent);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('Webhook 事件处理失败', [
                'event' => [
                    'namespace' => $event->namespace,
                    'cluster_id' => $event->clusterId,
                    'resource_type' => $event->resourceType,
                ],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }


}
