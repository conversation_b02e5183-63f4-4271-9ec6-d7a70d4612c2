<?php

namespace App\Jobs;

use App\Events\ResourceChanged;
use App\Listeners\ResourceChangedListener;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessWebhookEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public ResourceChanged $event
    ) {}

    public function handle(ResourceChangedListener $listener): void
    {
        $listener->handle($this->event);
    }
}
