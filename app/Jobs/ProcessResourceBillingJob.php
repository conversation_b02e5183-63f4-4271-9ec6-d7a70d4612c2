<?php

namespace App\Jobs;

use App\DTOs\Billing\PodUsageDTO;
use App\DTOs\Billing\ServiceUsageDTO;
use App\DTOs\Billing\StorageUsageDTO;
use App\Models\Cluster;
use App\Models\Workspace;
use App\Service\BalanceService;
use App\Service\Billing\BillingManager;
use App\Service\Billing\OverdueManagementService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessResourceBillingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 60;

    /**
     * 最大重试次数
     */
    public $tries = 3;

    /**
     * 创建新的任务实例
     */
    public function __construct(
        public Cluster $cluster,
        public string $resourceType,
        public array $resourceData,
        public string $workspaceName,
        public $billingTime
    ) {}

    /**
     * 执行任务
     */
    public function handle(
        BillingManager $billingManager,
        BalanceService $balanceService,
        OverdueManagementService $overdueManagementService
    ): void {
        try {
            // 获取工作空间
            $workspace = $this->getWorkspace();
            if (! $workspace) {
                Log::warning('工作空间不存在，跳过计费', [
                    'workspace_name' => $this->workspaceName,
                    'resource_type' => $this->resourceType,
                ]);

                return;
            }

            // 检查工作空间状态
            if ($workspace->status !== Workspace::STATUS_ACTIVE) {
                Log::debug('工作空间非活跃状态，跳过计费', [
                    'workspace_id' => $workspace->id,
                    'status' => $workspace->status,
                    'resource_type' => $this->resourceType,
                ]);

                return;
            }

            // 创建资源DTO
            $usageDTO = $this->createUsageDTO();
            if (! $usageDTO) {
                Log::warning('无法创建资源DTO，跳过计费', [
                    'workspace_id' => $workspace->id,
                    'resource_type' => $this->resourceType,
                ]);

                return;
            }

            // 获取对应的Handler
            $handler = $billingManager->getHandler($this->resourceType);
            if (! $handler) {
                Log::warning('未找到对应的计费Handler，跳过计费', [
                    'workspace_id' => $workspace->id,
                    'resource_type' => $this->resourceType,
                ]);

                return;
            }

            // 验证DTO
            if (! $handler->validateUsage($usageDTO)) {
                Log::warning('资源DTO验证失败，跳过计费', [
                    'workspace_id' => $workspace->id,
                    'resource_type' => $this->resourceType,
                ]);

                return;
            }

            // 计算价格
            $priceResource = $this->getPriceResource($workspace);
            $priceResult = $handler->calculatePrice($usageDTO, $priceResource, 1); // 按分钟计费

            // 如果费用为0，跳过计费
            if (bccomp($priceResult['total_cost'], '0', 8) <= 0) {
                // Log::debug('资源费用为0，跳过计费', [
                //     'workspace_id' => $workspace->id,
                //     'resource_type' => $this->resourceType,
                //     'resource_name' => $priceResult['resource_name'] ?? '',
                // ]);

                return;
            }

            // 执行扣费（带Redis锁）
            $this->performBilling($workspace, $priceResult, $balanceService, $overdueManagementService);

        } catch (\Exception $e) {
            Log::error('资源计费任务失败', [
                'cluster_id' => $this->cluster->id,
                'resource_type' => $this->resourceType,
                'workspace_name' => $this->workspaceName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * 获取工作空间
     */
    protected function getWorkspace(): ?Workspace
    {
        return Workspace::where('name', $this->workspaceName)
            ->where('cluster_id', $this->cluster->id)
            ->first();
    }

    /**
     * 创建资源DTO
     */
    protected function createUsageDTO()
    {
        return match ($this->resourceType) {
            'pod' => PodUsageDTO::fromK8sData($this->resourceData),
            'service' => ServiceUsageDTO::fromK8sData($this->resourceData),
            'storage' => StorageUsageDTO::fromK8sData($this->resourceData),
            default => null,
        };
    }

    /**
     * 获取价格资源
     */
    protected function getPriceResource(Workspace $workspace)
    {
        return match ($this->resourceType) {
            'pod', 'storage' => $workspace->cluster,
            'service' => $this->getServicePriceResource($workspace),
            default => null,
        };
    }

    /**
     * 获取Service的价格资源（IP Pool）
     */
    protected function getServicePriceResource(Workspace $workspace)
    {
        // 对于Service，需要通过数据库记录找到对应的IP Pool
        $serviceModel = \App\Models\Service::where('name', $this->resourceData['metadata']['name'] ?? '')
            ->where('workspace_id', $workspace->id)
            ->first();

        if ($serviceModel && $serviceModel->poolIp) {
            return $serviceModel->poolIp->ipPool;
        }

        // 如果没有找到对应的Service记录，返回null
        return null;
    }

    /**
     * 执行扣费
     */
    protected function performBilling(
        Workspace $workspace,
        array $priceResult,
        BalanceService $balanceService,
        OverdueManagementService $overdueManagementService
    ): void {
        $user = $workspace->user;
        $amount = $priceResult['total_cost'];
        $lockKey = "billing_lock:user:{$user->id}";

        // 使用缓存锁防止并发扣费冲突
        $lock = Cache::lock($lockKey, 30); // 30秒锁

        try {
            $lock->block(5); // 最多等待5秒获取锁

            DB::transaction(function () use ($user, $workspace, $amount, $priceResult, $balanceService, $overdueManagementService) {
                try {
                    // 刷新用户余额
                    $user->refresh();

                    // 执行扣费
                    $balanceService->deductBalance(
                        $user,
                        $amount,
                        sprintf(
                            '工作空间 %s %s 资源使用费',
                            $workspace->name,
                            $priceResult['resource_name'] ?? $this->resourceType
                        )
                    );

                    Log::info(sprintf('资源 %s:%s 计费成功, 扣费金额: %s', $this->resourceType, $priceResult['resource_name'] ?? '', $amount), [
                        'workspace_id' => $workspace->id,
                        'user_id' => $user->id,
                        'resource_type' => $this->resourceType,
                        'resource_name' => $priceResult['resource_name'] ?? '',
                        'amount' => $amount,
                        'new_balance' => $user->fresh()->current_balance,
                    ]);

                } catch (\Exception $e) {
                    // 扣费失败，处理欠费
                    Log::warning('资源计费失败，处理欠费', [
                        'workspace_id' => $workspace->id,
                        'user_id' => $user->id,
                        'resource_type' => $this->resourceType,
                        'amount' => $amount,
                        'error' => $e->getMessage(),
                    ]);

                    // 创建一个模拟的计费记录来处理欠费
                    $fakeRecord = new \App\Models\BillingRecord([
                        'workspace_id' => $workspace->id,
                        'cluster_id' => $workspace->cluster_id,
                        'user_id' => $user->id,
                        'total_cost' => $amount,
                        'billing_start_at' => $this->billingTime,
                        'billing_end_at' => now(),
                    ]);
                    $fakeRecord->save();

                    $overdueManagementService->handleUserOverdue($fakeRecord);
                }
            });

        } finally {
            $lock->release();
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('资源计费任务最终失败', [
            'cluster_id' => $this->cluster->id,
            'resource_type' => $this->resourceType,
            'workspace_name' => $this->workspaceName,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
