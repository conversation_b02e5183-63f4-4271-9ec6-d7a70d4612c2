<?php

namespace App\Events\K8s;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

abstract class BaseK8sEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $namespace;
    public string $clusterName;
    public int $clusterId;
    public string $resourceType;
    public string $resourceName;
    public array $resourceData;
    public array $metadata;
    public string $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        string $resourceName,
        array $resourceData,
        array $metadata = []
    ) {
        $this->namespace = $namespace;
        $this->clusterName = $clusterName;
        $this->clusterId = $clusterId;
        $this->resourceType = $resourceType;
        $this->resourceName = $resourceName;
        $this->resourceData = $resourceData;
        $this->metadata = $metadata;
        $this->timestamp = now()->toISOString();
    }

    /**
     * Get the webhook event type (e.g., "deployment.created")
     */
    abstract public function getWebhookEventType(): string;

    /**
     * Get the event action (e.g., "created", "updated", "deleted")
     */
    abstract public function getAction(): string;

    /**
     * Get the event description for display
     */
    abstract public function getDescription(): string;

    /**
     * Get additional event-specific data for webhook payload
     */
    public function getEventSpecificData(): array
    {
        return [];
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("workspace.{$this->namespace}.resources"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event_type' => $this->getWebhookEventType(),
            'action' => $this->getAction(),
            'namespace' => $this->namespace,
            'cluster' => [
                'id' => $this->clusterId,
                'name' => $this->clusterName,
            ],
            'resource' => [
                'type' => $this->resourceType,
                'name' => $this->resourceName,
                'data' => $this->resourceData,
            ],
            'metadata' => $this->metadata,
            'timestamp' => $this->timestamp,
            'description' => $this->getDescription(),
            'event_data' => $this->getEventSpecificData(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'k8s.resource.event';
    }

    /**
     * Get the webhook payload
     */
    public function getWebhookPayload(): array
    {
        return [
            'event' => [
                'type' => $this->getWebhookEventType(),
                'action' => $this->getAction(),
                'timestamp' => $this->timestamp,
                'id' => uniqid('k8s_event_', true),
                'description' => $this->getDescription(),
            ],
            'resource' => [
                'type' => $this->resourceType,
                'name' => $this->resourceName,
                'namespace' => $this->namespace,
                'data' => $this->resourceData,
            ],
            'cluster' => [
                'id' => $this->clusterId,
                'name' => $this->clusterName,
            ],
            'workspace' => [
                'namespace' => $this->namespace,
            ],
            'metadata' => $this->metadata,
            'event_data' => $this->getEventSpecificData(),
        ];
    }

    /**
     * Check if this event matches the given webhook filters
     */
    public function matchesWebhookFilters(array $resourceTypes, array $eventTypes): bool
    {
        // 检查资源类型
        if (!empty($resourceTypes) && !in_array($this->resourceType, $resourceTypes)) {
            return false;
        }

        // 检查事件类型 - 支持精确匹配和通配符匹配
        if (!empty($eventTypes)) {
            $eventType = $this->getWebhookEventType();
            $action = $this->getAction();
            
            // 检查精确匹配
            if (in_array($eventType, $eventTypes)) {
                return true;
            }
            
            // 检查通配符匹配 (如 "deployment.*" 匹配所有 deployment 事件)
            foreach ($eventTypes as $filterType) {
                if (str_ends_with($filterType, '.*')) {
                    $prefix = substr($filterType, 0, -2);
                    if (str_starts_with($eventType, $prefix . '.')) {
                        return true;
                    }
                }
                
                // 检查动作匹配 (如 "created" 匹配所有创建事件)
                if ($filterType === $action) {
                    return true;
                }
            }
            
            return false;
        }

        return true;
    }
}
