<?php

namespace App\Events\K8s\Service;

use App\Events\K8s\BaseK8sEvent;

class ServiceCreated extends BaseK8sEvent
{
    public function getWebhookEventType(): string
    {
        return 'service.created';
    }

    public function getAction(): string
    {
        return 'created';
    }

    public function getDescription(): string
    {
        $serviceType = $this->resourceData['spec']['type'] ?? 'ClusterIP';
        return "Service '{$this->resourceName}' of type '{$serviceType}' has been created in namespace '{$this->namespace}'";
    }

    public function getEventSpecificData(): array
    {
        return [
            'type' => $this->resourceData['spec']['type'] ?? 'ClusterIP',
            'cluster_ip' => $this->resourceData['spec']['clusterIP'] ?? null,
            'external_ips' => $this->resourceData['spec']['externalIPs'] ?? [],
            'load_balancer_ip' => $this->resourceData['spec']['loadBalancerIP'] ?? null,
            'ports' => $this->getServicePorts(),
            'selector' => $this->resourceData['spec']['selector'] ?? [],
            'session_affinity' => $this->resourceData['spec']['sessionAffinity'] ?? 'None',
            'labels' => $this->resourceData['metadata']['labels'] ?? [],
            'annotations' => $this->resourceData['metadata']['annotations'] ?? [],
        ];
    }

    private function getServicePorts(): array
    {
        $ports = [];
        $servicePorts = $this->resourceData['spec']['ports'] ?? [];
        
        foreach ($servicePorts as $port) {
            $ports[] = [
                'name' => $port['name'] ?? null,
                'port' => $port['port'] ?? null,
                'target_port' => $port['targetPort'] ?? null,
                'protocol' => $port['protocol'] ?? 'TCP',
                'node_port' => $port['nodePort'] ?? null,
            ];
        }
        
        return $ports;
    }
}
