<?php

namespace App\Events\K8s\Pod;

use App\Events\K8s\BaseK8sEvent;

class PodFailed extends BaseK8sEvent
{
    private string $reason;
    private string $message;

    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        string $resourceName,
        array $resourceData,
        string $reason = '',
        string $message = '',
        array $metadata = []
    ) {
        parent::__construct($namespace, $clusterName, $clusterId, $resourceType, $resourceName, $resourceData, $metadata);
        $this->reason = $reason ?: $this->extractFailureReason();
        $this->message = $message ?: $this->extractFailureMessage();
    }

    public function getWebhookEventType(): string
    {
        return 'pod.failed';
    }

    public function getAction(): string
    {
        return 'failed';
    }

    public function getDescription(): string
    {
        $reasonText = $this->reason ? " (Reason: {$this->reason})" : '';
        return "Pod '{$this->resourceName}' has failed in namespace '{$this->namespace}'{$reasonText}";
    }

    public function getEventSpecificData(): array
    {
        return [
            'phase' => $this->resourceData['status']['phase'] ?? 'Failed',
            'reason' => $this->reason,
            'message' => $this->message,
            'node_name' => $this->resourceData['spec']['nodeName'] ?? null,
            'containers' => $this->getFailedContainerInfo(),
            'conditions' => $this->resourceData['status']['conditions'] ?? [],
            'owner_references' => $this->resourceData['metadata']['ownerReferences'] ?? [],
            'labels' => $this->resourceData['metadata']['labels'] ?? [],
            'failure_time' => $this->extractFailureTime(),
        ];
    }

    private function extractFailureReason(): string
    {
        // 从 Pod 状态中提取失败原因
        $conditions = $this->resourceData['status']['conditions'] ?? [];
        
        foreach ($conditions as $condition) {
            if ($condition['type'] === 'PodReadyCondition' && $condition['status'] === 'False') {
                return $condition['reason'] ?? 'Unknown';
            }
        }
        
        // 从容器状态中提取失败原因
        $containerStatuses = $this->resourceData['status']['containerStatuses'] ?? [];
        foreach ($containerStatuses as $status) {
            if (isset($status['state']['terminated']['reason'])) {
                return $status['state']['terminated']['reason'];
            }
            if (isset($status['state']['waiting']['reason'])) {
                return $status['state']['waiting']['reason'];
            }
        }
        
        return $this->resourceData['status']['reason'] ?? 'Unknown';
    }

    private function extractFailureMessage(): string
    {
        // 从 Pod 状态中提取失败消息
        $conditions = $this->resourceData['status']['conditions'] ?? [];
        
        foreach ($conditions as $condition) {
            if ($condition['type'] === 'PodReadyCondition' && $condition['status'] === 'False') {
                return $condition['message'] ?? '';
            }
        }
        
        // 从容器状态中提取失败消息
        $containerStatuses = $this->resourceData['status']['containerStatuses'] ?? [];
        foreach ($containerStatuses as $status) {
            if (isset($status['state']['terminated']['message'])) {
                return $status['state']['terminated']['message'];
            }
            if (isset($status['state']['waiting']['message'])) {
                return $status['state']['waiting']['message'];
            }
        }
        
        return $this->resourceData['status']['message'] ?? '';
    }

    private function extractFailureTime(): ?string
    {
        $containerStatuses = $this->resourceData['status']['containerStatuses'] ?? [];
        
        foreach ($containerStatuses as $status) {
            if (isset($status['state']['terminated']['finishedAt'])) {
                return $status['state']['terminated']['finishedAt'];
            }
        }
        
        return null;
    }

    private function getFailedContainerInfo(): array
    {
        $containers = [];
        $containerSpecs = $this->resourceData['spec']['containers'] ?? [];
        $containerStatuses = $this->resourceData['status']['containerStatuses'] ?? [];
        
        foreach ($containerSpecs as $spec) {
            $status = collect($containerStatuses)->firstWhere('name', $spec['name']);
            
            $containers[] = [
                'name' => $spec['name'],
                'image' => $spec['image'],
                'ready' => $status['ready'] ?? false,
                'restart_count' => $status['restartCount'] ?? 0,
                'state' => $status['state'] ?? null,
                'last_state' => $status['lastState'] ?? null,
                'exit_code' => $status['state']['terminated']['exitCode'] ?? null,
            ];
        }
        
        return $containers;
    }
}
