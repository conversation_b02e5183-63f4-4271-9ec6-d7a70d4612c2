<?php

namespace App\Events\K8s\Pod;

use App\Events\K8s\BaseK8sEvent;

class PodCreated extends BaseK8sEvent
{
    public function getWebhookEventType(): string
    {
        return 'pod.created';
    }

    public function getAction(): string
    {
        return 'created';
    }

    public function getDescription(): string
    {
        return "Pod '{$this->resourceName}' has been created in namespace '{$this->namespace}'";
    }

    public function getEventSpecificData(): array
    {
        return [
            'phase' => $this->resourceData['status']['phase'] ?? 'Unknown',
            'node_name' => $this->resourceData['spec']['nodeName'] ?? null,
            'containers' => $this->getContainerInfo(),
            'owner_references' => $this->resourceData['metadata']['ownerReferences'] ?? [],
            'labels' => $this->resourceData['metadata']['labels'] ?? [],
            'annotations' => $this->resourceData['metadata']['annotations'] ?? [],
            'restart_policy' => $this->resourceData['spec']['restartPolicy'] ?? 'Always',
        ];
    }

    private function getContainerInfo(): array
    {
        $containers = [];
        $containerSpecs = $this->resourceData['spec']['containers'] ?? [];
        $containerStatuses = $this->resourceData['status']['containerStatuses'] ?? [];
        
        foreach ($containerSpecs as $spec) {
            $status = collect($containerStatuses)->firstWhere('name', $spec['name']);
            
            $containers[] = [
                'name' => $spec['name'],
                'image' => $spec['image'],
                'ready' => $status['ready'] ?? false,
                'restart_count' => $status['restartCount'] ?? 0,
                'state' => $status['state'] ?? null,
            ];
        }
        
        return $containers;
    }
}
