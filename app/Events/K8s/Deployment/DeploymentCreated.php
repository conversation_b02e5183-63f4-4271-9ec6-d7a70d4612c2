<?php

namespace App\Events\K8s\Deployment;

use App\Events\K8s\BaseK8sEvent;

class DeploymentCreated extends BaseK8sEvent
{
    public function getWebhookEventType(): string
    {
        return 'deployment.created';
    }

    public function getAction(): string
    {
        return 'created';
    }

    public function getDescription(): string
    {
        return "Deployment '{$this->resourceName}' has been created in namespace '{$this->namespace}'";
    }

    public function getEventSpecificData(): array
    {
        return [
            'replicas' => $this->resourceData['spec']['replicas'] ?? 0,
            'image' => $this->getContainerImages(),
            'strategy' => $this->resourceData['spec']['strategy']['type'] ?? 'RollingUpdate',
            'labels' => $this->resourceData['metadata']['labels'] ?? [],
            'annotations' => $this->resourceData['metadata']['annotations'] ?? [],
        ];
    }

    private function getContainerImages(): array
    {
        $images = [];
        $containers = $this->resourceData['spec']['template']['spec']['containers'] ?? [];
        
        foreach ($containers as $container) {
            $images[] = [
                'name' => $container['name'] ?? 'unknown',
                'image' => $container['image'] ?? 'unknown',
            ];
        }
        
        return $images;
    }
}
