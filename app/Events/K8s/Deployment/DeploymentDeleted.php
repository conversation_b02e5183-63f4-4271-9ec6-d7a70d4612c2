<?php

namespace App\Events\K8s\Deployment;

use App\Events\K8s\BaseK8sEvent;

class DeploymentDeleted extends BaseK8sEvent
{
    public function getWebhookEventType(): string
    {
        return 'deployment.deleted';
    }

    public function getAction(): string
    {
        return 'deleted';
    }

    public function getDescription(): string
    {
        return "Deployment '{$this->resourceName}' has been deleted from namespace '{$this->namespace}'";
    }

    public function getEventSpecificData(): array
    {
        return [
            'final_replicas' => $this->resourceData['spec']['replicas'] ?? 0,
            'final_images' => $this->getContainerImages(),
            'deletion_timestamp' => $this->resourceData['metadata']['deletionTimestamp'] ?? null,
            'labels' => $this->resourceData['metadata']['labels'] ?? [],
            'annotations' => $this->resourceData['metadata']['annotations'] ?? [],
        ];
    }

    private function getContainerImages(): array
    {
        $images = [];
        $containers = $this->resourceData['spec']['template']['spec']['containers'] ?? [];
        
        foreach ($containers as $container) {
            $images[] = [
                'name' => $container['name'] ?? 'unknown',
                'image' => $container['image'] ?? 'unknown',
            ];
        }
        
        return $images;
    }
}
