<?php

namespace App\Events\K8s\Deployment;

use App\Events\K8s\BaseK8sEvent;

class DeploymentScaled extends BaseK8sEvent
{
    private int $previousReplicas;
    private int $currentReplicas;

    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        string $resourceName,
        array $resourceData,
        int $previousReplicas,
        array $metadata = []
    ) {
        parent::__construct($namespace, $clusterName, $clusterId, $resourceType, $resourceName, $resourceData, $metadata);
        $this->previousReplicas = $previousReplicas;
        $this->currentReplicas = $resourceData['spec']['replicas'] ?? 0;
    }

    public function getWebhookEventType(): string
    {
        return 'deployment.scaled';
    }

    public function getAction(): string
    {
        return 'scaled';
    }

    public function getDescription(): string
    {
        $direction = $this->currentReplicas > $this->previousReplicas ? 'scaled up' : 'scaled down';
        return "Deployment '{$this->resourceName}' has been {$direction} from {$this->previousReplicas} to {$this->currentReplicas} replicas in namespace '{$this->namespace}'";
    }

    public function getEventSpecificData(): array
    {
        return [
            'previous_replicas' => $this->previousReplicas,
            'current_replicas' => $this->currentReplicas,
            'scale_direction' => $this->currentReplicas > $this->previousReplicas ? 'up' : 'down',
            'scale_factor' => abs($this->currentReplicas - $this->previousReplicas),
            'images' => $this->getContainerImages(),
            'labels' => $this->resourceData['metadata']['labels'] ?? [],
        ];
    }

    private function getContainerImages(): array
    {
        $images = [];
        $containers = $this->resourceData['spec']['template']['spec']['containers'] ?? [];
        
        foreach ($containers as $container) {
            $images[] = [
                'name' => $container['name'] ?? 'unknown',
                'image' => $container['image'] ?? 'unknown',
            ];
        }
        
        return $images;
    }
}
