<?php

namespace App\Events\K8s\Deployment;

use App\Events\K8s\BaseK8sEvent;

class DeploymentUpdated extends BaseK8sEvent
{
    private array $previousData;
    private array $changes;

    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        string $resourceName,
        array $resourceData,
        array $previousData = [],
        array $metadata = []
    ) {
        parent::__construct($namespace, $clusterName, $clusterId, $resourceType, $resourceName, $resourceData, $metadata);
        $this->previousData = $previousData;
        $this->changes = $this->calculateChanges();
    }

    public function getWebhookEventType(): string
    {
        return 'deployment.updated';
    }

    public function getAction(): string
    {
        return 'updated';
    }

    public function getDescription(): string
    {
        $changeDescriptions = [];
        
        if (!empty($this->changes['replicas'])) {
            $changeDescriptions[] = "replicas changed from {$this->changes['replicas']['from']} to {$this->changes['replicas']['to']}";
        }
        
        if (!empty($this->changes['images'])) {
            $changeDescriptions[] = "container images updated";
        }
        
        if (!empty($this->changes['strategy'])) {
            $changeDescriptions[] = "deployment strategy changed";
        }
        
        $changesText = empty($changeDescriptions) ? 'configuration updated' : implode(', ', $changeDescriptions);
        
        return "Deployment '{$this->resourceName}' has been updated in namespace '{$this->namespace}': {$changesText}";
    }

    public function getEventSpecificData(): array
    {
        return [
            'replicas' => $this->resourceData['spec']['replicas'] ?? 0,
            'image' => $this->getContainerImages(),
            'strategy' => $this->resourceData['spec']['strategy']['type'] ?? 'RollingUpdate',
            'labels' => $this->resourceData['metadata']['labels'] ?? [],
            'annotations' => $this->resourceData['metadata']['annotations'] ?? [],
            'changes' => $this->changes,
            'previous_data' => $this->previousData,
        ];
    }

    private function getContainerImages(): array
    {
        $images = [];
        $containers = $this->resourceData['spec']['template']['spec']['containers'] ?? [];
        
        foreach ($containers as $container) {
            $images[] = [
                'name' => $container['name'] ?? 'unknown',
                'image' => $container['image'] ?? 'unknown',
            ];
        }
        
        return $images;
    }

    private function calculateChanges(): array
    {
        $changes = [];
        
        // 检查副本数变化
        $currentReplicas = $this->resourceData['spec']['replicas'] ?? 0;
        $previousReplicas = $this->previousData['spec']['replicas'] ?? 0;
        
        if ($currentReplicas !== $previousReplicas) {
            $changes['replicas'] = [
                'from' => $previousReplicas,
                'to' => $currentReplicas,
            ];
        }
        
        // 检查镜像变化
        $currentImages = $this->getContainerImages();
        $previousImages = $this->getPreviousContainerImages();
        
        if ($currentImages !== $previousImages) {
            $changes['images'] = [
                'from' => $previousImages,
                'to' => $currentImages,
            ];
        }
        
        // 检查策略变化
        $currentStrategy = $this->resourceData['spec']['strategy']['type'] ?? 'RollingUpdate';
        $previousStrategy = $this->previousData['spec']['strategy']['type'] ?? 'RollingUpdate';
        
        if ($currentStrategy !== $previousStrategy) {
            $changes['strategy'] = [
                'from' => $previousStrategy,
                'to' => $currentStrategy,
            ];
        }
        
        return $changes;
    }

    private function getPreviousContainerImages(): array
    {
        $images = [];
        $containers = $this->previousData['spec']['template']['spec']['containers'] ?? [];
        
        foreach ($containers as $container) {
            $images[] = [
                'name' => $container['name'] ?? 'unknown',
                'image' => $container['image'] ?? 'unknown',
            ];
        }
        
        return $images;
    }
}
