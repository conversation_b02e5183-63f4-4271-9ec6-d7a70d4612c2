<?php

namespace App\Events\K8s;

use App\Events\K8s\Deployment\DeploymentCreated;
use App\Events\K8s\Deployment\DeploymentDeleted;
use App\Events\K8s\Deployment\DeploymentScaled;
use App\Events\K8s\Deployment\DeploymentUpdated;
use App\Events\K8s\Pod\PodCreated;
use App\Events\K8s\Pod\PodFailed;
use App\Events\K8s\Service\ServiceCreated;

class K8sEventFactory
{
    /**
     * 事件类型映射
     */
    private static array $eventMap = [
        // Deployment 事件
        'deployments.created' => DeploymentCreated::class,
        'deployments.updated' => DeploymentUpdated::class,
        'deployments.deleted' => DeploymentDeleted::class,
        'deployments.scaled' => DeploymentScaled::class,
        
        // Pod 事件
        'pods.created' => PodCreated::class,
        'pods.failed' => PodFailed::class,
        
        // Service 事件
        'services.created' => ServiceCreated::class,
        
        // 可以继续添加其他资源类型的事件...
    ];

    /**
     * 创建事件实例
     */
    public static function create(
        string $resourceType,
        string $action,
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceName,
        array $resourceData,
        array $additionalData = [],
        array $metadata = []
    ): ?BaseK8sEvent {
        $eventKey = "{$resourceType}.{$action}";
        
        if (!isset(self::$eventMap[$eventKey])) {
            // 如果没有找到具体的事件类，返回 null 或者可以创建一个通用事件
            return null;
        }
        
        $eventClass = self::$eventMap[$eventKey];
        
        // 根据不同的事件类型，传递不同的参数
        switch ($eventKey) {
            case 'deployments.updated':
                return new $eventClass(
                    $namespace,
                    $clusterName,
                    $clusterId,
                    $resourceType,
                    $resourceName,
                    $resourceData,
                    $additionalData['previous_data'] ?? [],
                    $metadata
                );
                
            case 'deployments.scaled':
                return new $eventClass(
                    $namespace,
                    $clusterName,
                    $clusterId,
                    $resourceType,
                    $resourceName,
                    $resourceData,
                    $additionalData['previous_replicas'] ?? 0,
                    $metadata
                );
                
            case 'pods.failed':
                return new $eventClass(
                    $namespace,
                    $clusterName,
                    $clusterId,
                    $resourceType,
                    $resourceName,
                    $resourceData,
                    $additionalData['reason'] ?? '',
                    $additionalData['message'] ?? '',
                    $metadata
                );
                
            default:
                return new $eventClass(
                    $namespace,
                    $clusterName,
                    $clusterId,
                    $resourceType,
                    $resourceName,
                    $resourceData,
                    $metadata
                );
        }
    }

    /**
     * 从资源变化数据批量创建事件
     */
    public static function createFromResourceChanges(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        array $changes,
        array $metadata = []
    ): array {
        $events = [];
        
        // 处理创建的资源
        foreach ($changes['created'] ?? [] as $resourceData) {
            $event = self::create(
                $resourceType,
                'created',
                $namespace,
                $clusterName,
                $clusterId,
                $resourceData['name'] ?? 'unknown',
                $resourceData,
                [],
                $metadata
            );
            
            if ($event) {
                $events[] = $event;
            }
        }
        
        // 处理更新的资源
        foreach ($changes['updated'] ?? [] as $resourceData) {
            $event = self::create(
                $resourceType,
                'updated',
                $namespace,
                $clusterName,
                $clusterId,
                $resourceData['name'] ?? 'unknown',
                $resourceData,
                [
                    'previous_data' => $resourceData['previous_data'] ?? [],
                ],
                $metadata
            );
            
            if ($event) {
                $events[] = $event;
            }
            
            // 检查是否是扩缩容操作
            if ($resourceType === 'deployments' && isset($resourceData['previous_data'])) {
                $currentReplicas = $resourceData['spec']['replicas'] ?? 0;
                $previousReplicas = $resourceData['previous_data']['spec']['replicas'] ?? 0;
                
                if ($currentReplicas !== $previousReplicas) {
                    $scaleEvent = self::create(
                        $resourceType,
                        'scaled',
                        $namespace,
                        $clusterName,
                        $clusterId,
                        $resourceData['name'] ?? 'unknown',
                        $resourceData,
                        [
                            'previous_replicas' => $previousReplicas,
                        ],
                        $metadata
                    );
                    
                    if ($scaleEvent) {
                        $events[] = $scaleEvent;
                    }
                }
            }
        }
        
        // 处理删除的资源
        foreach ($changes['deleted'] ?? [] as $resourceData) {
            $event = self::create(
                $resourceType,
                'deleted',
                $namespace,
                $clusterName,
                $clusterId,
                $resourceData['name'] ?? 'unknown',
                $resourceData,
                [],
                $metadata
            );
            
            if ($event) {
                $events[] = $event;
            }
        }
        
        return $events;
    }

    /**
     * 获取所有支持的事件类型
     */
    public static function getSupportedEventTypes(): array
    {
        return array_keys(self::$eventMap);
    }

    /**
     * 获取按资源类型分组的事件类型
     */
    public static function getEventTypesByResource(): array
    {
        $grouped = [];
        
        foreach (self::$eventMap as $eventKey => $eventClass) {
            [$resourceType, $action] = explode('.', $eventKey, 2);
            
            if (!isset($grouped[$resourceType])) {
                $grouped[$resourceType] = [];
            }
            
            $grouped[$resourceType][] = $action;
        }
        
        return $grouped;
    }

    /**
     * 检查事件类型是否支持
     */
    public static function isEventTypeSupported(string $resourceType, string $action): bool
    {
        return isset(self::$eventMap["{$resourceType}.{$action}"]);
    }
}
