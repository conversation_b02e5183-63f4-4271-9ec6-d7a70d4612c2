<?php

namespace App\Constants;

class WebhookEventTypes
{
    // 特殊事件类型
    public const ALL_EVENTS = 'all';

    // Deployment 事件
    public const DEPLOYMENT_CREATED = 'deployment.created';
    public const DEPLOYMENT_UPDATED = 'deployment.updated';
    public const DEPLOYMENT_DELETED = 'deployment.deleted';
    public const DEPLOYMENT_SCALED = 'deployment.scaled';
    public const DEPLOYMENT_ROLLOUT_STARTED = 'deployment.rollout_started';
    public const DEPLOYMENT_ROLLOUT_COMPLETED = 'deployment.rollout_completed';
    public const DEPLOYMENT_ROLLOUT_FAILED = 'deployment.rollout_failed';
    public const DEPLOYMENT_PAUSED = 'deployment.paused';
    public const DEPLOYMENT_RESUMED = 'deployment.resumed';

    // Pod 事件
    public const POD_CREATED = 'pod.created';
    public const POD_STARTED = 'pod.started';
    public const POD_READY = 'pod.ready';
    public const POD_FAILED = 'pod.failed';
    public const POD_SUCCEEDED = 'pod.succeeded';
    public const POD_DELETED = 'pod.deleted';
    public const POD_EVICTED = 'pod.evicted';
    public const POD_OOM_KILLED = 'pod.oom_killed';
    public const POD_IMAGE_PULL_FAILED = 'pod.image_pull_failed';
    public const POD_CRASH_LOOP_BACKOFF = 'pod.crash_loop_backoff';

    // Service 事件
    public const SERVICE_CREATED = 'service.created';
    public const SERVICE_UPDATED = 'service.updated';
    public const SERVICE_DELETED = 'service.deleted';
    public const SERVICE_ENDPOINT_READY = 'service.endpoint_ready';
    public const SERVICE_ENDPOINT_NOT_READY = 'service.endpoint_not_ready';

    // Ingress 事件
    public const INGRESS_CREATED = 'ingress.created';
    public const INGRESS_UPDATED = 'ingress.updated';
    public const INGRESS_DELETED = 'ingress.deleted';
    public const INGRESS_TLS_CONFIGURED = 'ingress.tls_configured';
    public const INGRESS_BACKEND_ERROR = 'ingress.backend_error';

    // PVC 事件
    public const PVC_CREATED = 'pvc.created';
    public const PVC_BOUND = 'pvc.bound';
    public const PVC_PENDING = 'pvc.pending';
    public const PVC_DELETED = 'pvc.deleted';
    public const PVC_RESIZE_STARTED = 'pvc.resize_started';
    public const PVC_RESIZE_COMPLETED = 'pvc.resize_completed';

    // Secret 事件
    public const SECRET_CREATED = 'secret.created';
    public const SECRET_UPDATED = 'secret.updated';
    public const SECRET_DELETED = 'secret.deleted';

    // ConfigMap 事件
    public const CONFIGMAP_CREATED = 'configmap.created';
    public const CONFIGMAP_UPDATED = 'configmap.updated';
    public const CONFIGMAP_DELETED = 'configmap.deleted';

    // HPA 事件
    public const HPA_CREATED = 'hpa.created';
    public const HPA_UPDATED = 'hpa.updated';
    public const HPA_DELETED = 'hpa.deleted';
    public const HPA_SCALED_UP = 'hpa.scaled_up';
    public const HPA_SCALED_DOWN = 'hpa.scaled_down';
    public const HPA_UNABLE_TO_SCALE = 'hpa.unable_to_scale';

    // 通配符事件
    public const DEPLOYMENT_ALL = 'deployment.*';
    public const POD_ALL = 'pod.*';
    public const SERVICE_ALL = 'service.*';
    public const INGRESS_ALL = 'ingress.*';
    public const PVC_ALL = 'pvc.*';
    public const SECRET_ALL = 'secret.*';
    public const CONFIGMAP_ALL = 'configmap.*';
    public const HPA_ALL = 'hpa.*';

    /**
     * 获取所有事件类型
     */
    public static function getAllEventTypes(): array
    {
        $reflection = new \ReflectionClass(self::class);
        return array_values($reflection->getConstants());
    }

    /**
     * 获取按资源分组的事件类型
     */
    public static function getEventTypesByResource(): array
    {
        return [
            'special' => [
                self::ALL_EVENTS,
            ],
            'deployment' => [
                self::DEPLOYMENT_ALL,
                self::DEPLOYMENT_CREATED,
                self::DEPLOYMENT_UPDATED,
                self::DEPLOYMENT_DELETED,
                self::DEPLOYMENT_SCALED,
                self::DEPLOYMENT_ROLLOUT_STARTED,
                self::DEPLOYMENT_ROLLOUT_COMPLETED,
                self::DEPLOYMENT_ROLLOUT_FAILED,
                self::DEPLOYMENT_PAUSED,
                self::DEPLOYMENT_RESUMED,
            ],
            'pod' => [
                self::POD_ALL,
                self::POD_CREATED,
                self::POD_STARTED,
                self::POD_READY,
                self::POD_FAILED,
                self::POD_SUCCEEDED,
                self::POD_DELETED,
                self::POD_EVICTED,
                self::POD_OOM_KILLED,
                self::POD_IMAGE_PULL_FAILED,
                self::POD_CRASH_LOOP_BACKOFF,
            ],
            'service' => [
                self::SERVICE_ALL,
                self::SERVICE_CREATED,
                self::SERVICE_UPDATED,
                self::SERVICE_DELETED,
                self::SERVICE_ENDPOINT_READY,
                self::SERVICE_ENDPOINT_NOT_READY,
            ],
            'ingress' => [
                self::INGRESS_ALL,
                self::INGRESS_CREATED,
                self::INGRESS_UPDATED,
                self::INGRESS_DELETED,
                self::INGRESS_TLS_CONFIGURED,
                self::INGRESS_BACKEND_ERROR,
            ],
            'storage' => [
                self::PVC_ALL,
                self::PVC_CREATED,
                self::PVC_BOUND,
                self::PVC_PENDING,
                self::PVC_DELETED,
                self::PVC_RESIZE_STARTED,
                self::PVC_RESIZE_COMPLETED,
            ],
            'config' => [
                self::SECRET_ALL,
                self::SECRET_CREATED,
                self::SECRET_UPDATED,
                self::SECRET_DELETED,
                self::CONFIGMAP_ALL,
                self::CONFIGMAP_CREATED,
                self::CONFIGMAP_UPDATED,
                self::CONFIGMAP_DELETED,
            ],
            'autoscaling' => [
                self::HPA_ALL,
                self::HPA_CREATED,
                self::HPA_UPDATED,
                self::HPA_DELETED,
                self::HPA_SCALED_UP,
                self::HPA_SCALED_DOWN,
                self::HPA_UNABLE_TO_SCALE,
            ],
        ];
    }

    /**
     * 检查是否是通配符事件类型
     */
    public static function isWildcardEventType(string $eventType): bool
    {
        return str_ends_with($eventType, '.*');
    }

    /**
     * 检查事件类型是否匹配过滤器
     */
    public static function eventMatches(string $eventType, array $filters): bool
    {
        if (empty($filters)) {
            return false;
        }

        // 检查是否监听所有事件
        if (in_array(self::ALL_EVENTS, $filters)) {
            return true;
        }

        // 精确匹配
        if (in_array($eventType, $filters)) {
            return true;
        }

        // 通配符匹配
        foreach ($filters as $filter) {
            if (self::isWildcardEventType($filter)) {
                $prefix = substr($filter, 0, -2);
                if (str_starts_with($eventType, $prefix . '.')) {
                    return true;
                }
            }
        }

        return false;
    }
}
