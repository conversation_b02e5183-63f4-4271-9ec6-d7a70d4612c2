<?php

namespace App\Constants;

class WebhookEventTypes
{
    // 特殊事件类型
    public const ALL_EVENTS = 'all';

    // Deployment 事件
    public const DEPLOYMENT_CREATED = 'deployment.created';
    public const DEPLOYMENT_UPDATED = 'deployment.updated';
    public const DEPLOYMENT_DELETED = 'deployment.deleted';
    public const DEPLOYMENT_SCALED = 'deployment.scaled';
    public const DEPLOYMENT_ROLLOUT_STARTED = 'deployment.rollout_started';
    public const DEPLOYMENT_ROLLOUT_COMPLETED = 'deployment.rollout_completed';
    public const DEPLOYMENT_ROLLOUT_FAILED = 'deployment.rollout_failed';
    public const DEPLOYMENT_PAUSED = 'deployment.paused';
    public const DEPLOYMENT_RESUMED = 'deployment.resumed';

    // Pod 事件
    public const POD_CREATED = 'pod.created';
    public const POD_STARTED = 'pod.started';
    public const POD_READY = 'pod.ready';
    public const POD_FAILED = 'pod.failed';
    public const POD_SUCCEEDED = 'pod.succeeded';
    public const POD_DELETED = 'pod.deleted';
    public const POD_EVICTED = 'pod.evicted';
    public const POD_OOM_KILLED = 'pod.oom_killed';
    public const POD_IMAGE_PULL_FAILED = 'pod.image_pull_failed';
    public const POD_CRASH_LOOP_BACKOFF = 'pod.crash_loop_backoff';

    // Service 事件
    public const SERVICE_CREATED = 'service.created';
    public const SERVICE_UPDATED = 'service.updated';
    public const SERVICE_DELETED = 'service.deleted';
    public const SERVICE_ENDPOINT_READY = 'service.endpoint_ready';
    public const SERVICE_ENDPOINT_NOT_READY = 'service.endpoint_not_ready';

    // Ingress 事件
    public const INGRESS_CREATED = 'ingress.created';
    public const INGRESS_UPDATED = 'ingress.updated';
    public const INGRESS_DELETED = 'ingress.deleted';
    public const INGRESS_TLS_CONFIGURED = 'ingress.tls_configured';
    public const INGRESS_BACKEND_ERROR = 'ingress.backend_error';

    // PVC 事件
    public const PVC_CREATED = 'pvc.created';
    public const PVC_BOUND = 'pvc.bound';
    public const PVC_PENDING = 'pvc.pending';
    public const PVC_DELETED = 'pvc.deleted';
    public const PVC_RESIZE_STARTED = 'pvc.resize_started';
    public const PVC_RESIZE_COMPLETED = 'pvc.resize_completed';

    // Secret 事件
    public const SECRET_CREATED = 'secret.created';
    public const SECRET_UPDATED = 'secret.updated';
    public const SECRET_DELETED = 'secret.deleted';

    // ConfigMap 事件
    public const CONFIGMAP_CREATED = 'configmap.created';
    public const CONFIGMAP_UPDATED = 'configmap.updated';
    public const CONFIGMAP_DELETED = 'configmap.deleted';

    // HPA 事件
    public const HPA_CREATED = 'hpa.created';
    public const HPA_UPDATED = 'hpa.updated';
    public const HPA_DELETED = 'hpa.deleted';
    public const HPA_SCALED_UP = 'hpa.scaled_up';
    public const HPA_SCALED_DOWN = 'hpa.scaled_down';
    public const HPA_UNABLE_TO_SCALE = 'hpa.unable_to_scale';

    // 通配符事件
    public const DEPLOYMENT_ALL = 'deployment.*';
    public const POD_ALL = 'pod.*';
    public const SERVICE_ALL = 'service.*';
    public const INGRESS_ALL = 'ingress.*';
    public const PVC_ALL = 'pvc.*';
    public const SECRET_ALL = 'secret.*';
    public const CONFIGMAP_ALL = 'configmap.*';
    public const HPA_ALL = 'hpa.*';

    /**
     * 获取所有事件类型
     */
    public static function getAllEventTypes(): array
    {
        $reflection = new \ReflectionClass(self::class);
        return array_values($reflection->getConstants());
    }

    /**
     * 获取所有事件类型（键值对格式，用于前端显示）
     */
    public static function getAllEventTypesWithLabels(): array
    {
        $eventTypes = self::getAllEventTypes();
        $labels = [];

        foreach ($eventTypes as $eventType) {
            $labels[$eventType] = self::getEventTypeLabel($eventType);
        }

        return $labels;
    }

    /**
     * 获取事件类型的人类可读标签
     */
    public static function getEventTypeLabel(string $eventType): string
    {
        $labels = [
            self::ALL_EVENTS => '所有事件',

            // Deployment 事件
            self::DEPLOYMENT_CREATED => 'Deployment 创建',
            self::DEPLOYMENT_UPDATED => 'Deployment 更新',
            self::DEPLOYMENT_DELETED => 'Deployment 删除',
            self::DEPLOYMENT_SCALED => 'Deployment 扩缩容',
            self::DEPLOYMENT_ROLLOUT_STARTED => 'Deployment 滚动更新开始',
            self::DEPLOYMENT_ROLLOUT_COMPLETED => 'Deployment 滚动更新完成',
            self::DEPLOYMENT_ROLLOUT_FAILED => 'Deployment 滚动更新失败',
            self::DEPLOYMENT_PAUSED => 'Deployment 暂停',
            self::DEPLOYMENT_RESUMED => 'Deployment 恢复',

            // Pod 事件
            self::POD_CREATED => 'Pod 创建',
            self::POD_STARTED => 'Pod 启动',
            self::POD_READY => 'Pod 就绪',
            self::POD_FAILED => 'Pod 失败',
            self::POD_SUCCEEDED => 'Pod 成功',
            self::POD_DELETED => 'Pod 删除',
            self::POD_EVICTED => 'Pod 被驱逐',
            self::POD_OOM_KILLED => 'Pod 内存溢出终止',
            self::POD_IMAGE_PULL_FAILED => 'Pod 镜像拉取失败',
            self::POD_CRASH_LOOP_BACKOFF => 'Pod 崩溃循环',

            // Service 事件
            self::SERVICE_CREATED => 'Service 创建',
            self::SERVICE_UPDATED => 'Service 更新',
            self::SERVICE_DELETED => 'Service 删除',
            self::SERVICE_ENDPOINT_READY => 'Service 端点就绪',
            self::SERVICE_ENDPOINT_NOT_READY => 'Service 端点未就绪',

            // Ingress 事件
            self::INGRESS_CREATED => 'Ingress 创建',
            self::INGRESS_UPDATED => 'Ingress 更新',
            self::INGRESS_DELETED => 'Ingress 删除',
            self::INGRESS_TLS_CONFIGURED => 'Ingress TLS 配置',
            self::INGRESS_BACKEND_ERROR => 'Ingress 后端错误',

            // PVC 事件
            self::PVC_CREATED => 'PVC 创建',
            self::PVC_BOUND => 'PVC 绑定',
            self::PVC_PENDING => 'PVC 等待中',
            self::PVC_DELETED => 'PVC 删除',
            self::PVC_RESIZE_STARTED => 'PVC 扩容开始',
            self::PVC_RESIZE_COMPLETED => 'PVC 扩容完成',

            // Secret 事件
            self::SECRET_CREATED => 'Secret 创建',
            self::SECRET_UPDATED => 'Secret 更新',
            self::SECRET_DELETED => 'Secret 删除',

            // ConfigMap 事件
            self::CONFIGMAP_CREATED => 'ConfigMap 创建',
            self::CONFIGMAP_UPDATED => 'ConfigMap 更新',
            self::CONFIGMAP_DELETED => 'ConfigMap 删除',

            // HPA 事件
            self::HPA_CREATED => 'HPA 创建',
            self::HPA_UPDATED => 'HPA 更新',
            self::HPA_DELETED => 'HPA 删除',
            self::HPA_SCALED_UP => 'HPA 扩容',
            self::HPA_SCALED_DOWN => 'HPA 缩容',
            self::HPA_UNABLE_TO_SCALE => 'HPA 无法扩缩容',

            // 通配符事件
            self::DEPLOYMENT_ALL => '所有 Deployment 事件',
            self::POD_ALL => '所有 Pod 事件',
            self::SERVICE_ALL => '所有 Service 事件',
            self::INGRESS_ALL => '所有 Ingress 事件',
            self::PVC_ALL => '所有 PVC 事件',
            self::SECRET_ALL => '所有 Secret 事件',
            self::CONFIGMAP_ALL => '所有 ConfigMap 事件',
            self::HPA_ALL => '所有 HPA 事件',
        ];

        return $labels[$eventType] ?? $eventType;
    }

    /**
     * 获取按资源分组的事件类型
     */
    public static function getEventTypesByResource(): array
    {
        return [
            'special' => [
                self::ALL_EVENTS,
            ],
            'deployment' => [
                self::DEPLOYMENT_ALL,
                self::DEPLOYMENT_CREATED,
                self::DEPLOYMENT_UPDATED,
                self::DEPLOYMENT_DELETED,
                self::DEPLOYMENT_SCALED,
                self::DEPLOYMENT_ROLLOUT_STARTED,
                self::DEPLOYMENT_ROLLOUT_COMPLETED,
                self::DEPLOYMENT_ROLLOUT_FAILED,
                self::DEPLOYMENT_PAUSED,
                self::DEPLOYMENT_RESUMED,
            ],
            'pod' => [
                self::POD_ALL,
                self::POD_CREATED,
                self::POD_STARTED,
                self::POD_READY,
                self::POD_FAILED,
                self::POD_SUCCEEDED,
                self::POD_DELETED,
                self::POD_EVICTED,
                self::POD_OOM_KILLED,
                self::POD_IMAGE_PULL_FAILED,
                self::POD_CRASH_LOOP_BACKOFF,
            ],
            'service' => [
                self::SERVICE_ALL,
                self::SERVICE_CREATED,
                self::SERVICE_UPDATED,
                self::SERVICE_DELETED,
                self::SERVICE_ENDPOINT_READY,
                self::SERVICE_ENDPOINT_NOT_READY,
            ],
            'ingress' => [
                self::INGRESS_ALL,
                self::INGRESS_CREATED,
                self::INGRESS_UPDATED,
                self::INGRESS_DELETED,
                self::INGRESS_TLS_CONFIGURED,
                self::INGRESS_BACKEND_ERROR,
            ],
            'storage' => [
                self::PVC_ALL,
                self::PVC_CREATED,
                self::PVC_BOUND,
                self::PVC_PENDING,
                self::PVC_DELETED,
                self::PVC_RESIZE_STARTED,
                self::PVC_RESIZE_COMPLETED,
            ],
            'config' => [
                self::SECRET_ALL,
                self::SECRET_CREATED,
                self::SECRET_UPDATED,
                self::SECRET_DELETED,
                self::CONFIGMAP_ALL,
                self::CONFIGMAP_CREATED,
                self::CONFIGMAP_UPDATED,
                self::CONFIGMAP_DELETED,
            ],
            'autoscaling' => [
                self::HPA_ALL,
                self::HPA_CREATED,
                self::HPA_UPDATED,
                self::HPA_DELETED,
                self::HPA_SCALED_UP,
                self::HPA_SCALED_DOWN,
                self::HPA_UNABLE_TO_SCALE,
            ],
        ];
    }

    /**
     * 检查是否是通配符事件类型
     */
    public static function isWildcardEventType(string $eventType): bool
    {
        return str_ends_with($eventType, '.*');
    }

    /**
     * 检查事件类型是否匹配过滤器
     */
    public static function eventMatches(string $eventType, array $filters): bool
    {
        if (empty($filters)) {
            return false;
        }

        // 检查是否监听所有事件
        if (in_array(self::ALL_EVENTS, $filters)) {
            return true;
        }

        // 精确匹配
        if (in_array($eventType, $filters)) {
            return true;
        }

        // 通配符匹配
        foreach ($filters as $filter) {
            if (self::isWildcardEventType($filter)) {
                $prefix = substr($filter, 0, -2);
                if (str_starts_with($eventType, $prefix . '.')) {
                    return true;
                }
            }
        }

        return false;
    }
}
