<?php

namespace App\Traits;

use App\Models\ResourcePricing;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

trait Pricable
{
    /**
     * 获取所有定价记录（用于历史查询）
     */
    public function pricings(): MorphMany
    {
        return $this->morphMany(ResourcePricing::class, 'resource');
    }

    /**
     * 获取当前有效的定价记录
     */
    public function currentPricing(): MorphOne
    {
        return $this->morphOne(ResourcePricing::class, 'resource')
            ->where('effective_date', '<=', now())
            ->latest('effective_date');
    }

    /**
     * 获取指定单位类型的当前定价
     */
    public function getCurrentPricingByUnit(string $unitType): ?ResourcePricing
    {
        return $this->pricings()
            ->where('unit_type', $unitType)
            ->where('effective_date', '<=', now())
            ->latest('effective_date')
            ->first();
    }

    /**
     * 创建新的定价记录
     */
    public function createPricing(array $attributes): ResourcePricing
    {
        return $this->pricings()->create(array_merge($attributes, [
            'effective_date' => $attributes['effective_date'] ?? now(),
        ]));
    }

    /**
     * 批量创建多种资源类型的定价
     */
    public function createMultiplePricings(array $pricingData): array
    {
        $createdPricings = [];

        foreach ($pricingData as $unitType => $data) {
            $createdPricings[$unitType] = $this->createPricing([
                'unit_type' => $unitType,
                'price_per_unit_per_month' => $data['price_per_unit_per_month'],
                'description' => $data['description'] ?? null,
                'effective_date' => $data['effective_date'] ?? now(),
            ]);
        }

        return $createdPricings;
    }

    /**
     * 更新定价（通过创建新记录的方式保持历史）
     */
    public function updatePricing(string $unitType, string $pricePerMonth, ?string $description = null, $effectiveDate = null): ResourcePricing
    {
        return $this->createPricing([
            'unit_type' => $unitType,
            'price_per_unit_per_month' => $pricePerMonth,
            'description' => $description,
            'effective_date' => $effectiveDate ?? now(),
        ]);
    }

    /**
     * 检查是否启用了计费
     */
    public function isBillingEnabled(): bool
    {
        return $this->pricings()
            ->where('effective_date', '<=', now())
            ->exists();
    }

    /**
     * 计算指定单位类型的每分钟价格
     */
    public function calculatePricePerMinute(string $unitType, float $usage = 1.0): string
    {
        $pricing = $this->getCurrentPricingByUnit($unitType);

        if (! $pricing) {
            return config('pricable.default_cost');
        }

        $pricePerMinute = $pricing->price_per_minute;

        return bcmul($pricePerMinute, (string) $usage, 8);
    }

    /**
     * 获取定价摘要
     */
    public function getPricingSummary(): array
    {
        $summary = [];

        // 获取当前模型对应的资源类型
        $resourceType = $this->getResourceType();

        // 只获取当前资源类型的单位类型
        $unitTypes = config("pricable.unit_types.{$resourceType}", []);

        foreach ($unitTypes as $unitType => $config) {
            $pricing = $this->getCurrentPricingByUnit($unitType);
            $summary[$unitType] = [
                'has_pricing' => ! is_null($pricing),
                'price_per_month' => $pricing?->price_per_unit_per_month ?? '0',
                'price_per_minute' => $pricing?->price_per_minute ?? '0',
                'effective_date' => $pricing?->effective_date?->toISOString(),
                'description' => $pricing?->description,
            ];
        }

        return $summary;
    }

    /**
     * 获取当前模型的资源类型
     */
    protected function getResourceType(): string
    {
        $modelClass = get_class($this);
        $models = config('pricable.models', []);

        return $models[$modelClass] ?? 'unknown';
    }
}
