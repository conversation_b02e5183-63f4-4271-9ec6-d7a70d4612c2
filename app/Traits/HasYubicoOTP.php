<?php

namespace App\Traits;

use App\Exceptions\Device\DeviceAlreadyBoundException;
use App\Models\YubicoOTP;

trait HasYubicoOTP
{
    /**
     * @throws DeviceAlreadyBoundException
     */
    public function addYubicoOTPDevice(string $device_id): YubicoOTP
    {
        // 检测是否被其他绑定
        if ($this->isRegisteredByOtpable($device_id)) {
            throw new DeviceAlreadyBoundException($device_id);
        }

        return $this->yubicoOtps()->create([
            'device_id' => $device_id,
        ]);
    }

    public function getYubicoOTPDevice()
    {
        return $this->yubicoOtps()->get();
    }

    public function removeYubicoOTPDevice(string $device_id): bool
    {
        return $this->yubicoOtps()->where('device_id', $device_id)->delete();
    }

    // remove all

    public function removeAllYubicoOTPDevice(): bool
    {
        return $this->yubicoOtps()->delete();
    }

    public function hasYubicoOTPDevice(string $device_id): bool
    {
        return $this->yubicoOtps()->where('device_id', $device_id)->exists();
    }

    public function hasYubicoOTP(): bool
    {
        return $this->yubicoOtps()->exists();
    }

    // 是否被其他模型注册过
    public function isRegisteredByOtpable(string $device_id): bool
    {
        return YubicoOTP::where('yubicoOtpable_type', get_class($this))
            ->where('device_id', $device_id)
            ->exists();
    }

    public function findByDeviceId(string $device_id): ?self
    {

        $y = YubicoOTP::where('yubicoOtpable_type', get_class($this))
            ->where('device_id', $device_id)
            ->first();

        if (! $y) {
            return null;
        }

        return $this->where('id', $y->yubicoOtpable_id)->first();
    }

    public function yubicoOtps()
    {
        return $this->morphMany(YubicoOTP::class, 'yubicoOtpable');
    }
}
