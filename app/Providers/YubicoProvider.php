<?php

namespace App\Providers;

use App\Contracts\YubicoOTP;
use App\Service\Auth\YubicoOTPService;
use Illuminate\Support\ServiceProvider;

class YubicoProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // bind and singleton
        $this->app->bind(YubicoOTP::class, YubicoOTPService::class);
        $this->app->singleton(YubicoOTPService::class, function () {
            return new YubicoOTPService(
                servers: config('yubico.otp_servers'),
                client_id: config('yubico.client_id'),
                client_secret: config('yubico.client_secret'),
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
