<?php

namespace App\Providers;

use App\Models\OAuthClient;
use App\Service\OAuth\AccessTokenResponse;
use App\Service\OAuth\IdTokenResponse;
use App\Service\OAuth\LaravelCurrentRequestService;
use Illuminate\Encryption\Encrypter;
use Lara<PERSON>\Passport\Bridge\AccessTokenRepository;
use Laravel\Passport\Bridge\ClientRepository;
use Laravel\Passport\Bridge\ScopeRepository;
use Laravel\Passport\Passport;
use Laravel\Passport\PassportServiceProvider as ServiceProvider;
use League\OAuth2\Server\AuthorizationServer;
use League\OAuth2\Server\ResponseTypes\ResponseTypeInterface;

class PassportServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        parent::register();
        $this->useStoragePassportKeys();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        parent::boot();
        // Passport Auth Cookie
        Passport::cookie('shared_api_cookie');
        Passport::useClientModel(OAuthClient::class);

        // Passport token lifetime configuration
        Passport::tokensExpireIn(now()->addMinutes(config('passport.token_lifetime.token')));
        Passport::refreshTokensExpireIn(now()->addMinutes(config('passport.token_lifetime.refresh_token')));
        Passport::personalAccessTokensExpireIn(now()->addMinutes(config('passport.token_lifetime.personal_access_token')));

        // Device Flow Views
        Passport::authorizationView('passport.authorize');
        Passport::deviceUserCodeView('passport.device.user-code');
        Passport::deviceAuthorizationView('passport.device.authorize');

        // Passport::authorizationView(
        //     fn ($parameters) => Inertia::render('auth/Authorize.Public', [
        //         'request' => $parameters['request'],
        //         'authToken' => $parameters['authToken'],
        //         'client' => $parameters['client'],
        //         'user' => $parameters['user'],
        //         'scopes' => $parameters['scopes'],
        //     ])
        // );

        $this->registerScopes();

    }

    private function useStoragePassportKeys(): void
    {
        if (empty(config('passport.private_key')) && file_exists(storage_path('oauth-private.key'))) {
            config(['passport.private_key' => file_get_contents(storage_path('oauth-private.key'))]);
        }

        if (empty(config('passport.public_key')) && file_exists(storage_path('oauth-public.key'))) {
            config(['passport.public_key' => file_get_contents(storage_path('oauth-public.key'))]);
        }
    }

    public function makeAuthorizationServer(?ResponseTypeInterface $responseType = null): AuthorizationServer
    {
        $cryptKey = $this->makeCryptKey('private');
        $encryptionKey = app(Encrypter::class)->getKey();

        $responseType = new IdTokenResponse(app(LaravelCurrentRequestService::class));

        return new AuthorizationServer(
            clientRepository: app(ClientRepository::class),
            accessTokenRepository: app(AccessTokenRepository::class),
            scopeRepository: app(ScopeRepository::class),
            privateKey: $cryptKey,
            encryptionKey: $encryptionKey,
            responseType: $responseType,
        );
    }

    private function registerScopes(): void
    {
        Passport::useAccessTokenEntity(AccessTokenResponse::class);
        // Passport::tokensExpireIn(now()->addMinutes(config('passport.token_lifetime.token')));
        // Passport::refreshTokensExpireIn(now()->addMinutes(config('passport.token_lifetime.refresh_token')));
        // Passport::personalAccessTokensExpireIn(now()->addMinutes(config('passport.token_lifetime.personal_access_token')));
        Passport::tokensCan(config('openid.passport.tokens_can'));
        Passport::$defaultScope = 'profile';
        // Passport::setDefaultScope(['profile']);
    }
}
