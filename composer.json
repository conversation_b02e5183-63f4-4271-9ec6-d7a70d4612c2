{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/vue-starter-kit", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.4", "ext-bcmath": "*", "amphp/websocket-client": "^2.0", "firebase/php-jwt": "^6.11", "inertiajs/inertia-laravel": "^2.0", "inspector-apm/neuron-ai": "^1.15", "laravel/framework": "^12.0", "laravel/octane": "^2.10", "laravel/passport": "^13.0", "laravel/reverb": "^1.0", "laravel/tinker": "^2.10.1", "lcobucci/jwt": "^5.5", "mikebronner/laravel-model-caching": "^12.0", "nyholm/psr7": "^1.8", "openai-php/client": "v0.10.2", "php-curl-class/php-curl-class": "^12.0", "react/socket": "^1.16", "rlanvin/php-ip": "^3.0", "spatie/laravel-passkeys": "^1.0", "spatie/laravel-permission": "^6.20", "spatie/laravel-webhook-server": "*", "symfony/yaml": "^7.3", "tightenco/ziggy": "^2.4", "workerman/channel": "^1.2", "workerman/http-client": "^3.0", "workerman/workerman": "^5.1"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel-lang/lang": "^15.22", "laravel/pail": "^1.2.2", "laravel/pint": "^1.18", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.2"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan lang:update", "@php ./vendor/bin/pint"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan reverb:start\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,reverb,queue,logs,vite --kill-others"], "dev:ssr": ["npm run build:ssr", "Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"php artisan inertia:start-ssr\" --names=server,queue,logs,ssr --kill-others"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}