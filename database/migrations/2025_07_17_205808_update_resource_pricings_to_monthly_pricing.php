<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('resource_pricings', function (Blueprint $table) {
            // 添加新的月价格字段
            $table->decimal('price_per_unit_per_month', 12, 8)->nullable()->comment('每单位每月价格');
        });

        // 数据迁移：将小时价格转换为月价格
        // 假设一个月平均有 730 小时 (365天 * 24小时 / 12个月)
        DB::statement('UPDATE resource_pricings SET price_per_unit_per_month = price_per_unit_per_hour * 730');

        Schema::table('resource_pricings', function (Blueprint $table) {
            // 设置新字段为非空
            $table->decimal('price_per_unit_per_month', 12, 8)->nullable(false)->change();
            // 删除旧的小时价格字段
            $table->dropColumn('price_per_unit_per_hour');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('resource_pricings', function (Blueprint $table) {
            // 重新添加小时价格字段
            $table->decimal('price_per_unit_per_hour', 12, 8)->nullable()->comment('每单位每小时价格');
        });

        // 数据迁移：将月价格转换为小时价格
        DB::statement('UPDATE resource_pricings SET price_per_unit_per_hour = price_per_unit_per_month / 730');

        Schema::table('resource_pricings', function (Blueprint $table) {
            // 设置字段为非空
            $table->decimal('price_per_unit_per_hour', 12, 8)->nullable(false)->change();
            // 删除月价格字段
            $table->dropColumn('price_per_unit_per_month');
        });
    }
};
