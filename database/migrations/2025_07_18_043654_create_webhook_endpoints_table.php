<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_endpoints', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->string('name')->comment('Webhook 端点名称');
            $table->string('url')->comment('Webhook URL');
            $table->string('secret')->nullable()->comment('Webhook 签名密钥');
            $table->json('event_types')->default('["all"]')->comment('监听的事件类型');
            $table->boolean('listen_all_events')->default(true)->comment('是否监听所有事件');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->text('description')->nullable()->comment('描述');
            $table->integer('timeout')->default(30)->comment('超时时间（秒）');
            $table->integer('max_retries')->default(3)->comment('最大重试次数');
            $table->timestamps();

            // 性能优化索引
            $table->index(['workspace_id', 'is_active', 'listen_all_events'], 'webhook_performance_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_endpoints');
    }
};
