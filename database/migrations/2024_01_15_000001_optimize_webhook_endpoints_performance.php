<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('webhook_endpoints', function (Blueprint $table) {
            // 添加性能优化字段
            $table->boolean('listen_all_events')->default(false)->after('event_types')
                ->comment('是否监听所有事件类型，用于优化查询性能');
            
            // 添加索引优化查询
            $table->index(['workspace_id', 'is_active', 'listen_all_events'], 'webhook_performance_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('webhook_endpoints', function (Blueprint $table) {
            $table->dropIndex('webhook_performance_idx');
            $table->dropColumn('listen_all_events');
        });
    }
};
