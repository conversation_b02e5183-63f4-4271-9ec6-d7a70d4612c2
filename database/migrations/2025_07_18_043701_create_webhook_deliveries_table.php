<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_deliveries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('webhook_endpoint_id')->constrained()->onDelete('cascade');
            $table->string('event_type')->comment('事件类型：deployment.created, pod.failed 等');
            $table->string('resource_name')->comment('资源名称');
            $table->string('namespace')->comment('命名空间');
            $table->string('cluster_name')->comment('集群名称');
            $table->integer('cluster_id')->comment('集群ID');
            $table->json('payload')->comment('发送的 payload 数据');
            $table->string('status')->default('pending')->comment('发送状态：pending, success, failed');
            $table->integer('attempts')->default(0)->comment('尝试次数');
            $table->integer('response_status')->nullable()->comment('HTTP 响应状态码');
            $table->text('response_body')->nullable()->comment('响应内容');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamp('delivered_at')->nullable()->comment('成功发送时间');
            $table->timestamp('failed_at')->nullable()->comment('最终失败时间');
            $table->timestamps();

            // 索引
            $table->index(['webhook_endpoint_id', 'status']);
            $table->index(['event_type']);
            $table->index(['namespace', 'cluster_id']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_deliveries');
    }
};
