<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('webhook_endpoints', function (Blueprint $table) {
            // 移除资源类型相关字段
            $table->dropColumn('resource_types');
        });

        Schema::table('webhook_deliveries', function (Blueprint $table) {
            // 移除资源类型相关字段
            $table->dropColumn('resource_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('webhook_endpoints', function (Blueprint $table) {
            $table->json('resource_types')->nullable()->after('event_types');
        });

        Schema::table('webhook_deliveries', function (Blueprint $table) {
            $table->string('resource_type')->after('event_type');
        });
    }
};
