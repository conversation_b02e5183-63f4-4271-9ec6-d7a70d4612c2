<?php

use App\Events\ResourceChanged;
use App\Models\User;
use App\Models\WebhookEndpoint;
use App\Models\Workspace;
use Illuminate\Support\Facades\Event;

test('can create webhook endpoint', function () {
    $user = User::factory()->create();
    $workspace = Workspace::factory()->active()->create([
        'user_id' => $user->id,
    ]);
    $user->update(['current_workspace_id' => $workspace->id]);

    // 调试信息
    dump([
        'workspace_status' => $workspace->status,
        'workspace_is_active' => $workspace->isActive(),
        'user_current_workspace_id' => $user->current_workspace_id,
    ]);

    $response = $this->actingAs($user, 'api')
        ->postJson('/api/webhook-endpoints', [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'secret' => 'test-secret',
            'resource_types' => ['deployments', 'services'],
            'event_types' => ['created', 'updated'],
            'description' => 'Test webhook description',
            'timeout' => 30,
            'max_retries' => 3,
        ]);

    if ($response->status() !== 201) {
        dump($response->json());
    }

    $response->assertStatus(201);
    $this->assertDatabaseHas('webhook_endpoints', [
        'workspace_id' => $workspace->id,
        'name' => 'Test Webhook',
        'url' => 'https://example.com/webhook',
    ]);
});

test('can list webhook endpoints', function () {
    $user = User::factory()->create();
    $workspace = Workspace::factory()->active()->create([
        'user_id' => $user->id,
    ]);
    $user->update(['current_workspace_id' => $workspace->id]);

    WebhookEndpoint::create([
        'workspace_id' => $workspace->id,
        'name' => 'Test Webhook 1',
        'url' => 'https://example.com/webhook1',
        'resource_types' => ['deployments'],
        'event_types' => ['created'],
    ]);

    WebhookEndpoint::create([
        'workspace_id' => $workspace->id,
        'name' => 'Test Webhook 2',
        'url' => 'https://example.com/webhook2',
        'resource_types' => ['services'],
        'event_types' => ['updated'],
    ]);

    $response = $this->actingAs($user, 'api')
        ->getJson('/api/webhook-endpoints');

    $response->assertStatus(200);
    $response->assertJsonCount(2, 'data');
});

test('can get webhook options', function () {
    $user = User::factory()->create();
    $workspace = Workspace::factory()->active()->create([
        'user_id' => $user->id,
    ]);
    $user->update(['current_workspace_id' => $workspace->id]);

    $response = $this->actingAs($user, 'api')
        ->getJson('/api/webhook-options');

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'resource_types',
        'event_types',
    ]);
});
