<?php

use App\Events\K8s\Deployment\DeploymentCreated;
use App\Events\K8s\K8sEventFactory;
use App\Events\ResourceChanged;
use App\Models\User;
use App\Models\WebhookEndpoint;
use App\Models\Workspace;
use Illuminate\Support\Facades\Event;

test('can create webhook endpoint', function () {
    $user = User::factory()->create();
    $workspace = Workspace::factory()->active()->create([
        'user_id' => $user->id,
    ]);
    $user->update(['current_workspace_id' => $workspace->id]);

    // 调试信息
    dump([
        'workspace_status' => $workspace->status,
        'workspace_is_active' => $workspace->isActive(),
        'user_current_workspace_id' => $user->current_workspace_id,
    ]);

    $response = $this->actingAs($user, 'api')
        ->postJson('/api/webhook-endpoints', [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'secret' => 'test-secret',
            'resource_types' => ['deployments', 'services'],
            'event_types' => ['created', 'updated'],
            'description' => 'Test webhook description',
            'timeout' => 30,
            'max_retries' => 3,
        ]);

    if ($response->status() !== 201) {
        dump($response->json());
    }

    $response->assertStatus(201);
    $this->assertDatabaseHas('webhook_endpoints', [
        'workspace_id' => $workspace->id,
        'name' => 'Test Webhook',
        'url' => 'https://example.com/webhook',
    ]);
});

test('can list webhook endpoints', function () {
    $user = User::factory()->create();
    $workspace = Workspace::factory()->active()->create([
        'user_id' => $user->id,
    ]);
    $user->update(['current_workspace_id' => $workspace->id]);

    WebhookEndpoint::create([
        'workspace_id' => $workspace->id,
        'name' => 'Test Webhook 1',
        'url' => 'https://example.com/webhook1',
        'resource_types' => ['deployments'],
        'event_types' => ['created'],
    ]);

    WebhookEndpoint::create([
        'workspace_id' => $workspace->id,
        'name' => 'Test Webhook 2',
        'url' => 'https://example.com/webhook2',
        'resource_types' => ['services'],
        'event_types' => ['updated'],
    ]);

    $response = $this->actingAs($user, 'api')
        ->getJson('/api/webhook-endpoints');

    $response->assertStatus(200);
    $response->assertJsonCount(2, 'data');
});

test('can get webhook options', function () {
    $user = User::factory()->create();
    $workspace = Workspace::factory()->active()->create([
        'user_id' => $user->id,
    ]);
    $user->update(['current_workspace_id' => $workspace->id]);

    $response = $this->actingAs($user, 'api')
        ->getJson('/api/webhook-options');

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'resource_types',
        'event_types',
    ]);
});

test('can create detailed k8s events from factory', function () {
    $event = K8sEventFactory::create(
        'deployments',
        'created',
        'test-namespace',
        'test-cluster',
        1,
        'test-deployment',
        [
            'metadata' => [
                'name' => 'test-deployment',
                'labels' => ['app' => 'test'],
            ],
            'spec' => [
                'replicas' => 3,
                'template' => [
                    'spec' => [
                        'containers' => [
                            [
                                'name' => 'app',
                                'image' => 'nginx:latest',
                            ]
                        ]
                    ]
                ]
            ]
        ]
    );

    expect($event)->toBeInstanceOf(DeploymentCreated::class);
    expect($event->getWebhookEventType())->toBe('deployment.created');
    expect($event->getAction())->toBe('created');
    expect($event->resourceName)->toBe('test-deployment');
    expect($event->namespace)->toBe('test-namespace');
});

test('webhook endpoint matches event types correctly', function () {
    $endpoint = new WebhookEndpoint([
        'event_types' => ['deployment.created', 'pod.*', 'created']
    ]);

    // 精确匹配
    expect($endpoint->listensToEventType('deployment.created'))->toBeTrue();

    // 通配符匹配
    expect($endpoint->listensToEventType('pod.failed'))->toBeTrue();
    expect($endpoint->listensToEventType('pod.ready'))->toBeTrue();

    // 动作匹配
    expect($endpoint->listensToEventType('service.created'))->toBeTrue();

    // 不匹配
    expect($endpoint->listensToEventType('deployment.updated'))->toBeFalse();
    expect($endpoint->listensToEventType('service.updated'))->toBeFalse();
});

test('k8s event factory creates events from resource changes', function () {
    $changes = [
        'created' => [
            [
                'name' => 'test-deployment',
                'spec' => ['replicas' => 3],
            ]
        ],
        'updated' => [
            [
                'name' => 'test-deployment-2',
                'spec' => ['replicas' => 5],
                'previous_data' => [
                    'spec' => ['replicas' => 3]
                ]
            ]
        ],
        'deleted' => [
            [
                'name' => 'test-deployment-3',
                'spec' => ['replicas' => 2],
            ]
        ]
    ];

    $events = K8sEventFactory::createFromResourceChanges(
        'test-namespace',
        'test-cluster',
        1,
        'deployments',
        $changes
    );

    expect($events)->toHaveCount(4); // created, updated, scaled, deleted
    expect($events[0]->getWebhookEventType())->toBe('deployment.created');
    expect($events[1]->getWebhookEventType())->toBe('deployment.updated');
    expect($events[2]->getWebhookEventType())->toBe('deployment.scaled');
    expect($events[3]->getWebhookEventType())->toBe('deployment.deleted');
});
