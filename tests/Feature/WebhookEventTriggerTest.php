<?php

namespace Tests\Feature;

use App\Events\ResourceChanged;
use App\Models\User;
use App\Models\Workspace;
use App\Models\WebhookEndpoint;
use App\Models\WebhookDelivery;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WebhookEventTriggerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Workspace $workspace;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'namespace' => 'test-namespace',
            'cluster_id' => 1,
        ]);
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function can_manually_trigger_deployment_created_event()
    {
        // 创建监听所有事件的 webhook
        $allEventsWebhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'All Events Webhook',
            'event_types' => ['all'],
            'listen_all_events' => true,
            'url' => 'https://httpbin.org/post',
        ]);

        // 创建监听特定事件的 webhook
        $specificWebhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'Deployment Events Webhook',
            'event_types' => ['deployment.created', 'deployment.updated'],
            'listen_all_events' => false,
            'url' => 'https://httpbin.org/post',
        ]);

        // 手动触发 ResourceChanged 事件
        $event = new ResourceChanged(
            namespace: 'test-namespace',
            clusterName: 'test-cluster',
            clusterId: 1,
            resourceType: 'deployments',
            changes: [
                'created' => [
                    [
                        'name' => 'test-deployment',
                        'namespace' => 'test-namespace',
                        'replicas' => 3,
                        'image' => 'nginx:latest',
                        'status' => 'Running',
                        'created_at' => now()->toISOString(),
                    ]
                ]
            ],
            summary: 'Deployment test-deployment was created'
        );

        // 使用队列来处理事件
        Queue::fake();
        Event::dispatch($event);

        // 验证事件被分发
        Queue::assertPushed(\App\Jobs\ProcessWebhookEvent::class);
    }

    /** @test */
    public function can_manually_trigger_pod_failed_event()
    {
        // 创建监听 Pod 事件的 webhook
        $podWebhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'Pod Events Webhook',
            'event_types' => ['pod.*'], // 使用通配符
            'listen_all_events' => false,
            'url' => 'https://httpbin.org/post',
        ]);

        // 手动触发 Pod 失败事件
        $event = new ResourceChanged(
            namespace: 'test-namespace',
            clusterName: 'test-cluster',
            clusterId: 1,
            resourceType: 'pods',
            changes: [
                'updated' => [
                    [
                        'name' => 'test-pod',
                        'namespace' => 'test-namespace',
                        'status' => 'Failed',
                        'reason' => 'ImagePullBackOff',
                        'message' => 'Failed to pull image',
                        'restart_count' => 5,
                        'updated_at' => now()->toISOString(),
                    ]
                ]
            ],
            summary: 'Pod test-pod failed with ImagePullBackOff'
        );

        Queue::fake();
        Event::dispatch($event);

        Queue::assertPushed(\App\Jobs\ProcessWebhookEvent::class);
    }

    /** @test */
    public function can_manually_trigger_service_updated_event()
    {
        // 创建监听 Service 事件的 webhook
        $serviceWebhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'Service Events Webhook',
            'event_types' => ['service.updated'],
            'listen_all_events' => false,
            'url' => 'https://httpbin.org/post',
        ]);

        // 手动触发 Service 更新事件
        $event = new ResourceChanged(
            namespace: 'test-namespace',
            clusterName: 'test-cluster',
            clusterId: 1,
            resourceType: 'services',
            changes: [
                'updated' => [
                    [
                        'name' => 'test-service',
                        'namespace' => 'test-namespace',
                        'type' => 'LoadBalancer',
                        'ports' => [
                            ['port' => 80, 'targetPort' => 8080, 'protocol' => 'TCP']
                        ],
                        'external_ip' => '*************',
                        'updated_at' => now()->toISOString(),
                    ]
                ]
            ],
            summary: 'Service test-service was updated'
        );

        Queue::fake();
        Event::dispatch($event);

        Queue::assertPushed(\App\Jobs\ProcessWebhookEvent::class);
    }

    /** @test */
    public function can_trigger_multiple_resource_changes()
    {
        // 创建监听所有事件的 webhook
        $webhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'All Events Webhook',
            'event_types' => ['all'],
            'listen_all_events' => true,
            'url' => 'https://httpbin.org/post',
        ]);

        // 手动触发包含多个资源变更的事件
        $event = new ResourceChanged(
            namespace: 'test-namespace',
            clusterName: 'test-cluster',
            clusterId: 1,
            resourceType: 'deployments',
            changes: [
                'created' => [
                    [
                        'name' => 'deployment-1',
                        'namespace' => 'test-namespace',
                        'replicas' => 2,
                        'image' => 'nginx:1.20',
                    ],
                    [
                        'name' => 'deployment-2',
                        'namespace' => 'test-namespace',
                        'replicas' => 3,
                        'image' => 'redis:6.2',
                    ]
                ],
                'updated' => [
                    [
                        'name' => 'deployment-3',
                        'namespace' => 'test-namespace',
                        'replicas' => 5,
                        'image' => 'postgres:13',
                    ]
                ],
                'deleted' => [
                    [
                        'name' => 'old-deployment',
                        'namespace' => 'test-namespace',
                    ]
                ]
            ],
            summary: 'Multiple deployment changes occurred'
        );

        Queue::fake();
        Event::dispatch($event);

        Queue::assertPushed(\App\Jobs\ProcessWebhookEvent::class);
    }

    /** @test */
    public function webhook_delivery_records_are_created()
    {
        // 不使用队列，直接处理事件
        $webhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'Test Webhook',
            'event_types' => ['all'],
            'listen_all_events' => true,
            'url' => 'https://httpbin.org/post',
        ]);

        // 手动触发事件
        $event = new ResourceChanged(
            namespace: 'test-namespace',
            clusterName: 'test-cluster',
            clusterId: 1,
            resourceType: 'deployments',
            changes: [
                'created' => [
                    [
                        'name' => 'test-deployment',
                        'namespace' => 'test-namespace',
                        'replicas' => 1,
                    ]
                ]
            ],
            summary: 'Test deployment created'
        );

        // 直接分发事件（不使用队列）
        Event::dispatch($event);

        // 验证 WebhookDelivery 记录被创建
        $this->assertDatabaseHas('webhook_deliveries', [
            'webhook_endpoint_id' => $webhook->id,
            'event_type' => 'deployment.created',
            'resource_name' => 'test-deployment',
            'namespace' => 'test-namespace',
            'cluster_name' => 'test-cluster',
        ]);
    }

    /** @test */
    public function only_matching_webhooks_receive_events()
    {
        // 创建监听 deployment 事件的 webhook
        $deploymentWebhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'event_types' => ['deployment.*'],
            'listen_all_events' => false,
            'url' => 'https://httpbin.org/post',
        ]);

        // 创建监听 pod 事件的 webhook
        $podWebhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'event_types' => ['pod.*'],
            'listen_all_events' => false,
            'url' => 'https://httpbin.org/post',
        ]);

        // 触发 deployment 事件
        $event = new ResourceChanged(
            namespace: 'test-namespace',
            clusterName: 'test-cluster',
            clusterId: 1,
            resourceType: 'deployments',
            changes: [
                'created' => [
                    ['name' => 'test-deployment', 'namespace' => 'test-namespace']
                ]
            ],
            summary: 'Deployment created'
        );

        Event::dispatch($event);

        // 验证只有 deployment webhook 收到事件
        $this->assertDatabaseHas('webhook_deliveries', [
            'webhook_endpoint_id' => $deploymentWebhook->id,
            'event_type' => 'deployment.created',
        ]);

        $this->assertDatabaseMissing('webhook_deliveries', [
            'webhook_endpoint_id' => $podWebhook->id,
        ]);
    }

    /** @test */
    public function inactive_webhooks_do_not_receive_events()
    {
        // 创建非活跃的 webhook
        $inactiveWebhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'event_types' => ['all'],
            'listen_all_events' => true,
            'is_active' => false,
            'url' => 'https://httpbin.org/post',
        ]);

        // 触发事件
        $event = new ResourceChanged(
            namespace: 'test-namespace',
            clusterName: 'test-cluster',
            clusterId: 1,
            resourceType: 'deployments',
            changes: [
                'created' => [
                    ['name' => 'test-deployment', 'namespace' => 'test-namespace']
                ]
            ],
            summary: 'Deployment created'
        );

        Event::dispatch($event);

        // 验证非活跃的 webhook 没有收到事件
        $this->assertDatabaseMissing('webhook_deliveries', [
            'webhook_endpoint_id' => $inactiveWebhook->id,
        ]);
    }
}
