<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use App\Models\WebhookEndpoint;
use App\Models\WebhookDelivery;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WebhookCrudTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Workspace $workspace;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
        ]);
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function can_get_webhook_options()
    {
        $response = $this->getJson('/api/webhook-endpoints/options');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'event_types',
                'event_types_grouped'
            ]);

        $data = $response->json();
        $this->assertArrayHasKey('all', $data['event_types']);
        $this->assertArrayHasKey('deployment.created', $data['event_types']);
        $this->assertArrayHasKey('pod.failed', $data['event_types']);
    }

    /** @test */
    public function can_create_webhook_with_all_events()
    {
        $webhookData = [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'secret' => 'test-secret',
            'description' => 'Test webhook description',
            'event_types' => ['all'],
            'timeout' => 30,
            'max_retries' => 3,
        ];

        $response = $this->postJson('/api/webhook-endpoints', $webhookData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'url',
                    'event_types',
                    'listen_all_events',
                    'is_active',
                    'timeout',
                    'max_retries',
                ]
            ]);

        $webhook = WebhookEndpoint::first();
        $this->assertEquals('Test Webhook', $webhook->name);
        $this->assertEquals('https://example.com/webhook', $webhook->url);
        $this->assertEquals(['all'], $webhook->event_types);
        $this->assertTrue($webhook->listen_all_events);
        $this->assertTrue($webhook->is_active);
    }

    /** @test */
    public function can_create_webhook_with_specific_events()
    {
        $webhookData = [
            'name' => 'Specific Events Webhook',
            'url' => 'https://example.com/webhook',
            'event_types' => ['deployment.created', 'pod.failed'],
            'timeout' => 60,
            'max_retries' => 5,
        ];

        $response = $this->postJson('/api/webhook-endpoints', $webhookData);

        $response->assertStatus(201);

        $webhook = WebhookEndpoint::first();
        $this->assertEquals(['deployment.created', 'pod.failed'], $webhook->event_types);
        $this->assertFalse($webhook->listen_all_events);
    }

    /** @test */
    public function can_list_webhooks()
    {
        WebhookEndpoint::factory()->count(3)->create([
            'workspace_id' => $this->workspace->id,
        ]);

        $response = $this->getJson('/api/webhook-endpoints');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'url',
                        'event_types',
                        'listen_all_events',
                        'is_active',
                    ]
                ]
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    /** @test */
    public function can_show_specific_webhook()
    {
        $webhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'Test Webhook',
        ]);

        $response = $this->getJson("/api/webhook-endpoints/{$webhook->id}");

        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'id' => $webhook->id,
                    'name' => 'Test Webhook',
                ]
            ]);
    }

    /** @test */
    public function can_update_webhook()
    {
        $webhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'Original Name',
            'event_types' => ['deployment.created'],
            'listen_all_events' => false,
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'event_types' => ['all'],
            'timeout' => 45,
        ];

        $response = $this->putJson("/api/webhook-endpoints/{$webhook->id}", $updateData);

        $response->assertStatus(200);

        $webhook->refresh();
        $this->assertEquals('Updated Name', $webhook->name);
        $this->assertEquals(['all'], $webhook->event_types);
        $this->assertTrue($webhook->listen_all_events);
        $this->assertEquals(45, $webhook->timeout);
    }

    /** @test */
    public function can_delete_webhook()
    {
        $webhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
        ]);

        $response = $this->deleteJson("/api/webhook-endpoints/{$webhook->id}");

        $response->assertStatus(204);
        $this->assertDatabaseMissing('webhook_endpoints', ['id' => $webhook->id]);
    }

    /** @test */
    public function can_test_webhook()
    {
        $webhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'url' => 'https://httpbin.org/post', // 使用真实的测试端点
        ]);

        $response = $this->postJson("/api/webhook-endpoints/{$webhook->id}/test");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'response_status',
                'response_time',
            ]);
    }

    /** @test */
    public function can_get_webhook_deliveries()
    {
        $webhook = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
        ]);

        // 创建一些投递记录
        WebhookDelivery::factory()->count(3)->create([
            'webhook_endpoint_id' => $webhook->id,
        ]);

        $response = $this->getJson("/api/webhook-endpoints/{$webhook->id}/deliveries");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'event_type',
                        'status',
                        'attempts',
                        'created_at',
                    ]
                ]
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    /** @test */
    public function validates_webhook_creation_data()
    {
        $response = $this->postJson('/api/webhook-endpoints', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'url', 'event_types']);
    }

    /** @test */
    public function validates_webhook_url_format()
    {
        $webhookData = [
            'name' => 'Test Webhook',
            'url' => 'invalid-url',
            'event_types' => ['all'],
        ];

        $response = $this->postJson('/api/webhook-endpoints', $webhookData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['url']);
    }

    /** @test */
    public function validates_event_types_not_empty_when_not_all()
    {
        $webhookData = [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'event_types' => [],
        ];

        $response = $this->postJson('/api/webhook-endpoints', $webhookData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['event_types']);
    }

    /** @test */
    public function automatically_sets_listen_all_events_flag()
    {
        // 测试包含 'all' 的情况
        $webhookData = [
            'name' => 'All Events Webhook',
            'url' => 'https://example.com/webhook',
            'event_types' => ['all'],
        ];

        $response = $this->postJson('/api/webhook-endpoints', $webhookData);
        $response->assertStatus(201);

        $webhook = WebhookEndpoint::first();
        $this->assertTrue($webhook->listen_all_events);

        // 测试不包含 'all' 的情况
        $webhookData2 = [
            'name' => 'Specific Events Webhook',
            'url' => 'https://example.com/webhook2',
            'event_types' => ['deployment.created'],
        ];

        $response2 = $this->postJson('/api/webhook-endpoints', $webhookData2);
        $response2->assertStatus(201);

        $webhook2 = WebhookEndpoint::latest()->first();
        $this->assertFalse($webhook2->listen_all_events);
    }
}
