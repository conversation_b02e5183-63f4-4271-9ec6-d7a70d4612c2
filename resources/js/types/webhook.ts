export interface WebhookEndpoint {
    id: number;
    name: string;
    url: string;
    resource_types: string[];
    event_types: string[];
    is_active: boolean;
    description?: string;
    timeout: number;
    max_retries: number;
    created_at: string;
    updated_at: string;
    recent_deliveries?: WebhookDelivery[];
    deliveries_count?: number;
    success_rate?: number;
}

export interface WebhookDelivery {
    id: number;
    event_type: string;
    resource_type: string;
    resource_name: string;
    namespace: string;
    cluster_name: string;
    cluster_id: number;
    status: 'pending' | 'success' | 'failed';
    attempts: number;
    response_status?: number;
    response_body?: string;
    error_message?: string;
    payload?: any;
    delivered_at?: string;
    failed_at?: string;
    created_at: string;
    updated_at: string;
}

export interface WebhookOptions {
    resource_types: Record<string, string>;
    event_types: Record<string, string>;
}

export interface WebhookFormData {
    name: string;
    url: string;
    secret?: string;
    resource_types: string[];
    event_types: string[];
    description?: string;
    timeout?: number;
    max_retries?: number;
    is_active?: boolean;
}

export interface WebhookTestResult {
    success: boolean;
    status_code?: number;
    response_body?: string;
    error?: string;
}
