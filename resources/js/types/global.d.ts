declare global {
    interface Window {
        desk: {
            on: (type: string, callback: (payload: any) => void) => () => void;
            emit: (type: string, payload?: any) => void;
            request: (method: string, payload?: any, timeout?: number) => Promise<any>;
            provide: (method: string, handler: (payload: any) => any | Promise<any>) => void;
        };
        // Passkey/WebAuthn functions
        browserSupportsWebAuthn: () => boolean;
        startRegistration: (options: any) => Promise<any>;
        startAuthentication: (options: any) => Promise<any>;
        // Route helper
        route: (name: string, params?: any) => string;
    }
}
export {};
