<script setup lang="ts">
import UserInfo from '@/components/UserInfo.vue';
import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import type { User } from '@/types';
import { Link, router } from '@inertiajs/vue3';
import { KeyIcon, Lock, LogOut, Settings } from 'lucide-vue-next';

interface Props {
    user: User;
}

const handleLogout = () => {
    router.flushAll();
};

defineProps<Props>();
</script>

<template>
    <DropdownMenuLabel class="p-0 font-normal">
        <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <UserInfo :user="user" :show-email="true" />
        </div>
    </DropdownMenuLabel>
    <DropdownMenuSeparator />
    <DropdownMenuGroup>
        <DropdownMenuItem :as-child="true">
            <Link class="block w-full" :href="route('profile.edit')" prefetch as="button">
            <Settings class="mr-2 h-4 w-4" />
            设置
            </Link>
        </DropdownMenuItem>
        <DropdownMenuItem :as-child="true">
            <Link class="block w-full" :href="route('passkey.index')" prefetch as="button">
            <KeyIcon class="mr-2 h-4 w-4" />
            通行密钥
            </Link>
        </DropdownMenuItem>
        <DropdownMenuItem :as-child="true">
            <Link class="block w-full" :href="route('otp.index')" prefetch as="button">
            <KeyIcon class="mr-2 h-4 w-4" />
            YubiKey 设置
            </Link>
        </DropdownMenuItem>
        <DropdownMenuItem :as-child="true">
            <Link :href="route('oauth.tokens.index')">
            <Lock class="mr-2 h-4 w-4" />
            <span>访问令牌</span>
            </Link>
        </DropdownMenuItem>
    </DropdownMenuGroup>
    <DropdownMenuSeparator />
    <DropdownMenuItem :as-child="true">
        <Link class="block w-full" method="post" :href="route('logout')" @click="handleLogout" as="button">
        <LogOut class="mr-2 h-4 w-4" />
        登出
        </Link>
    </DropdownMenuItem>
</template>
