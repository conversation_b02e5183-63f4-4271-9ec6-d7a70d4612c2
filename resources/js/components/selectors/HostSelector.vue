<template>
    <div class="space-y-2">
        <Label v-if="label">{{ label }}</Label>
        <div class="space-y-2">
            <div v-for="(selectedHost, index) in internalHosts" :key="index" class="flex items-center space-x-2">
                <Input
                    :model-value="selectedHost || ''"
                    :placeholder="placeholder"
                    :title="`输入第 ${index + 1} 个域名`"
                    @update:model-value="(value) => updateHostValue(index, String(value))"
                    class="flex-1"
                />
                <Button type="button" variant="outline" size="icon" :title="`删除第 ${index + 1} 个域名`" @click="removeHost(index)">
                    <Trash2 class="h-4 w-4" />
                </Button>
            </div>
            <Button type="button" variant="outline" :title="addButtonText" @click="addHost" class="w-full">
                <Plus class="mr-2 h-4 w-4" />
                {{ addButtonText }}
            </Button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Trash2 } from 'lucide-vue-next';
import { computed } from 'vue';

interface Props {
    modelValue: string[] | null | undefined;
    label?: string;
    placeholder?: string;
    addButtonText?: string;
}

const props = withDefaults(defineProps<Props>(), {
    label: '',
    placeholder: '输入域名',
    addButtonText: '添加域名',
});

const emit = defineEmits<{
    'update:modelValue': [value: string[]];
}>();

const internalHosts = computed(() => props.modelValue ?? []);

const addHost = () => {
    const newValue = [...internalHosts.value, ''];
    emit('update:modelValue', newValue);
};

const removeHost = (index: number) => {
    const newValue = internalHosts.value.filter((_, i) => i !== index);
    emit('update:modelValue', newValue);
};

const updateHostValue = (index: number, value: string) => {
    const newValue = [...internalHosts.value];
    newValue[index] = value || '';
    emit('update:modelValue', newValue);
};
</script>
