<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { router } from '@inertiajs/vue3';
import { CheckIcon, KeyIcon, KeyboardIcon, LockIcon, ShieldCheckIcon, XIcon } from 'lucide-vue-next';
import { computed, onMounted, onUnmounted, ref } from 'vue';

interface Props {
    title?: string;
    description?: string;
    submitRoute: string;
    footerText?: string;
    isAdmin?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    title: 'Yubico OTP 登录',
    description: '请插入并触摸 YubiKey',
    footerText: 'Powered by Leaflow & Yubico OTP',
    isAdmin: false,
});

const otpInput = ref<HTMLInputElement | null>(null);
const form = ref({
    otp: '',
});

// 定义状态类型
type StatusType = 'WAITING' | 'INPUTTING' | 'READY' | 'VERIFYING' | 'SUCCESS' | 'ERROR';

// 状态配置
const CONFIG = {
    STATUS_TEXT: {
        WAITING: '请插入并触摸 YubiKey',
        INPUTTING: '验证中...',
        READY: '按回车确认',
        VERIFYING: '正在验证...',
        SUCCESS: '验证成功',
        ERROR: '验证失败，请重试',
    } as Record<StatusType, string>,
    COLORS: {
        DANGER: 'hsl(var(--destructive))',
        WARNING: 'hsl(var(--warning))',
        PRIMARY: 'hsl(var(--primary))',
        SUCCESS: 'hsl(var(--success))',
    },
    ANIMATION: {
        TOTAL_LENGTH: 44, // YubiKey OTP 标准长度
        CIRCLE_LENGTH: 565.48,
        INPUT_TIMEOUT: 100,
        ERROR_RESET_DELAY: 2000,
        SUCCESS_REDIRECT_DELAY: 500,
    },
};

// 缓冲区和状态
const buffer = ref(''); // OTP 字符缓冲区
const currentStatus = ref<StatusType>('WAITING');
const isVerifying = ref(false);
const hasCompleteInput = ref(false);
const lastInputTime = ref(Date.now());
const autoSubmitTimeout = ref<number | null>(null);

// 动画相关
const currentProgress = ref(0);
const targetProgress = ref(0);
const circumference = computed(() => 2 * Math.PI * 90); // 进度环周长
const dashOffset = computed(() => {
    return circumference.value - currentProgress.value * circumference.value;
});

// 计算属性
const statusText = computed(() => props.description || CONFIG.STATUS_TEXT[currentStatus.value]);
const progressRingColor = computed(() => {
    if (currentStatus.value === 'SUCCESS') {
        return CONFIG.COLORS.SUCCESS;
    }
    if (currentStatus.value === 'ERROR') {
        return CONFIG.COLORS.DANGER;
    }
    if (currentProgress.value < 0.3) {
        return CONFIG.COLORS.DANGER;
    }
    if (currentProgress.value < 0.6) {
        return CONFIG.COLORS.WARNING;
    }
    if (currentProgress.value < 1) {
        return CONFIG.COLORS.PRIMARY;
    }
    return CONFIG.COLORS.SUCCESS;
});

const buttonBgColor = computed(() => {
    if (currentStatus.value === 'SUCCESS') {
        return CONFIG.COLORS.SUCCESS;
    }
    if (currentStatus.value === 'ERROR') {
        return CONFIG.COLORS.DANGER;
    }
    return 'hsl(var(--muted-foreground))';
});

// 状态图标组件
const statusIconComponent = computed(() => {
    switch (currentStatus.value) {
        case 'SUCCESS':
            return CheckIcon;
        case 'ERROR':
            return XIcon;
        default:
            return LockIcon;
    }
});

// 更新状态
const updateStatus = (status: StatusType, progress: number | null = null) => {
    currentStatus.value = status;

    if (progress !== null) {
        targetProgress.value = progress;
        requestAnimationFrame(animate);
    }
};

// 显示错误
const showError = (errorMessage: string) => {
    currentProgress.value = 0;
    targetProgress.value = 0;
    hasCompleteInput.value = false;
    buffer.value = '';
    form.value.otp = '';
    updateStatus('ERROR', 0);

    // 显示错误提示
    const statusElem = document.querySelector('.status-message');
    if (statusElem) {
        statusElem.textContent = errorMessage;
    }

    setTimeout(() => {
        updateStatus('WAITING', 0);
    }, CONFIG.ANIMATION.ERROR_RESET_DELAY);

    isVerifying.value = false;
};

// 重置输入
const resetInput = () => {
    buffer.value = '';
    hasCompleteInput.value = false;
    targetProgress.value = 0;
    updateStatus('WAITING', 0);
};

// 处理输入
const handleInput = (char: string) => {
    if (isVerifying.value) return;

    lastInputTime.value = Date.now();

    // 如果之前有错误，确保完全重置状态
    if (currentStatus.value === 'ERROR') {
        resetInput();
        return;
    }

    buffer.value += char;

    if (buffer.value.length === CONFIG.ANIMATION.TOTAL_LENGTH) {
        hasCompleteInput.value = true;
        updateStatus('READY', 1);
    } else if (buffer.value.length < CONFIG.ANIMATION.TOTAL_LENGTH) {
        hasCompleteInput.value = false;
        const progress = buffer.value.length / CONFIG.ANIMATION.TOTAL_LENGTH;
        updateStatus('INPUTTING', progress);
    } else {
        buffer.value = buffer.value.slice(-CONFIG.ANIMATION.TOTAL_LENGTH);
        hasCompleteInput.value = true;
        updateStatus('READY', 1);
    }
};

// 验证 YubiKey OTP
const validateYubicoOTP = (otp: string) => {
    return otp.length === CONFIG.ANIMATION.TOTAL_LENGTH;
};

// 动画循环
const animate = () => {
    const now = Date.now();

    // 如果超时没有进一步输入，重置
    if (!hasCompleteInput.value && now - lastInputTime.value > CONFIG.ANIMATION.INPUT_TIMEOUT && targetProgress.value > 0) {
        targetProgress.value = 0;
        buffer.value = '';
        updateStatus('WAITING', 0);
    }

    // 平滑过渡
    const diff = targetProgress.value - currentProgress.value;

    if (Math.abs(diff) < 0.01) {
        currentProgress.value = targetProgress.value;
        return;
    }

    currentProgress.value += diff * 0.2; // 平滑因子

    // 继续动画
    requestAnimationFrame(animate);
};

// 提交表单
const submitForm = async (otp?: string) => {
    if (isVerifying.value) return;

    if (otp) {
        form.value.otp = otp;
    } else if (!form.value.otp && buffer.value) {
        form.value.otp = buffer.value;
    }

    if (!form.value.otp) return;

    isVerifying.value = true;
    updateStatus('VERIFYING', 1);

    try {
        await router.post(props.submitRoute, form.value, {
            onSuccess: () => {
                updateStatus('SUCCESS', 1);
                setTimeout(() => {
                    window.location.reload();
                }, CONFIG.ANIMATION.SUCCESS_REDIRECT_DELAY);
            },
            onError: (errors) => {
                if (errors.otp) {
                    showError(errors.otp);
                } else {
                    showError('验证失败，请重试');
                }
            },
        });
    } catch (e) {
        showError('网络错误，请重试');
    }
};

// 聚焦输入框
const focusInput = () => {
    if (otpInput.value) {
        otpInput.value.focus();
    }
};

// 处理表单输入事件
const handleFormInput = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const inputValue = target.value;
    const lastChar = inputValue[inputValue.length - 1] || '';

    if (lastChar === '\n' || lastChar === '\r') return;

    handleInput(lastChar);

    if (inputValue.length === CONFIG.ANIMATION.TOTAL_LENGTH) {
        const otp = inputValue;
        if (validateYubicoOTP(otp)) {
            hasCompleteInput.value = true;
            updateStatus('READY', 1);

            // 清除之前的超时
            if (autoSubmitTimeout.value) {
                clearTimeout(autoSubmitTimeout.value);
            }

            // 如果是移动设备，设置自动提交超时
            if (window.innerWidth < 768) {
                autoSubmitTimeout.value = window.setTimeout(() => {
                    if (hasCompleteInput.value && !isVerifying.value) {
                        submitForm(otp);
                    }
                }, CONFIG.ANIMATION.INPUT_TIMEOUT);
            }
        }
    }
};

// 处理全局键盘事件
const handleKeyPress = (e: KeyboardEvent) => {
    if (isVerifying.value) return;

    if (e.key === 'Enter') {
        if (autoSubmitTimeout.value) {
            clearTimeout(autoSubmitTimeout.value);
        }
        if (buffer.value.length === CONFIG.ANIMATION.TOTAL_LENGTH) {
            const otp = buffer.value;
            if (validateYubicoOTP(otp)) {
                submitForm(otp);
            }
        }
        e.preventDefault();
        return;
    }

    handleInput(e.key);
};

onMounted(() => {
    // 设置初始状态
    updateStatus('WAITING', 0);

    // 添加全局键盘事件监听
    document.addEventListener('keypress', handleKeyPress);
});

onUnmounted(() => {
    // 移除事件监听
    document.removeEventListener('keypress', handleKeyPress);

    // 清除超时
    if (autoSubmitTimeout.value) {
        clearTimeout(autoSubmitTimeout.value);
    }
});
</script>

<template>
    <Card class="border-0 shadow-lg">
        <CardHeader class="space-y-1 text-center">
            <CardTitle class="text-xl">{{ title }}</CardTitle>
            <CardDescription class="status-message">{{ statusText }}</CardDescription>
        </CardHeader>
        <CardContent>
            <form @submit.prevent class="space-y-4">
                <input type="password" v-model="form.otp" class="sr-only" autocomplete="off" ref="otpInput" @input="handleFormInput" />

                <div class="flex justify-center">
                    <div class="relative mx-auto h-40 w-40">
                        <!-- 进度环 -->
                        <svg class="absolute inset-0 h-full w-full -rotate-90" viewBox="0 0 200 200">
                            <circle class="progress-ring-circle-bg" cx="100" cy="100" r="90" />
                            <circle
                                cx="100"
                                cy="100"
                                r="90"
                                :style="{
                                    stroke: progressRingColor,
                                    strokeDasharray: `${circumference} ${circumference}`,
                                    strokeDashoffset: dashOffset,
                                    fill: 'none',
                                    strokeWidth: 4,
                                    strokeLinecap: 'round',
                                    transition: 'stroke 0.3s ease',
                                }"
                            />
                        </svg>

                        <!-- Yubikey 设备 -->
                        <div class="absolute top-1/2 left-1/2 h-[100px] w-[60px] -translate-x-1/2 -translate-y-1/2">
                            <div
                                class="relative h-[85%] w-full rounded-t-lg border border-b-0 border-white/10 bg-gray-800 transition-all duration-300"
                            >
                                <div
                                    class="absolute top-1/2 left-1/2 flex h-[30px] w-[30px] -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full border border-white/10 transition-all duration-300"
                                    :style="{ backgroundColor: buttonBgColor }"
                                >
                                    <component :is="statusIconComponent" class="h-4 w-4 text-white" />
                                </div>
                            </div>
                            <div
                                class="absolute bottom-0 left-1/2 h-[15%] w-[70%] -translate-x-1/2 rounded-b border border-t-0 border-white/10 bg-gray-800 transition-all duration-300"
                            >
                                <div
                                    class="absolute top-1/2 left-1/2 h-[60%] w-[80%] -translate-x-1/2 -translate-y-1/2 rounded border border-white/15 bg-gray-600"
                                ></div>
                                <div class="absolute top-1/2 left-1/2 h-[30%] w-[60%] -translate-x-1/2 -translate-y-1/2 rounded-sm bg-gray-500"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 移动设备输入按钮 -->
                <div class="text-center md:hidden">
                    <Button variant="outline" type="button" @click="focusInput">
                        <KeyboardIcon class="mr-2 h-4 w-4" />
                        打开输入键盘
                    </Button>
                </div>
            </form>

            <div class="mt-6 space-y-4">
                <div class="flex items-start">
                    <KeyIcon class="mt-0.5 h-5 w-5 flex-shrink-0 text-primary" />
                    <div class="ml-3">
                        <h6 class="text-sm font-medium">使用说明</h6>
                        <p class="text-sm text-muted-foreground">
                            PC端：直接触摸 YubiKey 按钮<br />
                            移动端：点击输入按钮后触摸
                        </p>
                    </div>
                </div>
                <div class="flex items-start">
                    <ShieldCheckIcon class="mt-0.5 h-5 w-5 flex-shrink-0 text-primary" />
                    <div class="ml-3">
                        <h6 class="text-sm font-medium">安全提示</h6>
                        <p class="text-sm text-muted-foreground">请确保设备已正确连接并等待按钮闪烁</p>
                    </div>
                </div>
            </div>
        </CardContent>
        <CardFooter>
            <div class="w-full">
                <p class="text-center text-xs text-muted-foreground">
                    {{ footerText }}
                </p>
            </div>
        </CardFooter>
    </Card>
</template>

<style scoped>
.progress-ring-circle-bg {
    fill: none;
    stroke-width: 4;
    stroke: hsl(var(--border));
}

/* 暗色模式适配 */
:deep(.dark) .progress-ring-circle-bg {
    stroke: hsl(var(--border));
}
</style>
