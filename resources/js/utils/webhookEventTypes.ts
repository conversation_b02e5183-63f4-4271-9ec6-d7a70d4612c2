import type { EventTypeGroup } from '@/types/webhook';

/**
 * 将事件类型按资源分组
 */
export function groupEventTypes(eventTypes: Record<string, string>): EventTypeGroup[] {
    const groups: Record<string, EventTypeGroup> = {};
    
    // 通用事件组
    groups.general = {
        label: '通用事件',
        events: []
    };
    
    // 资源特定事件组
    const resourceGroups: Record<string, string> = {
        deployment: 'Deployment 事件',
        pod: 'Pod 事件',
        service: 'Service 事件',
        ingress: 'Ingress 事件',
        pvc: 'PVC 事件',
        secret: 'Secret 事件',
        configmap: 'ConfigMap 事件',
        hpa: 'HPA 事件',
    };
    
    // 初始化资源组
    Object.entries(resourceGroups).forEach(([key, label]) => {
        groups[key] = {
            label,
            events: []
        };
    });
    
    // 分类事件类型
    Object.entries(eventTypes).forEach(([value, label]) => {
        const event = { value, label };
        
        // 检查是否是通配符事件
        if (value.includes('.*')) {
            const resourceType = value.split('.')[0];
            if (groups[resourceType]) {
                groups[resourceType].events.push({
                    ...event,
                    description: `监听所有 ${resourceGroups[resourceType]} 事件`
                });
            }
            return;
        }
        
        // 检查是否是资源特定事件
        if (value.includes('.')) {
            const resourceType = value.split('.')[0];
            if (groups[resourceType]) {
                groups[resourceType].events.push(event);
                return;
            }
        }
        
        // 通用事件
        groups.general.events.push(event);
    });
    
    // 过滤掉空组并排序
    return Object.values(groups)
        .filter(group => group.events.length > 0)
        .map(group => ({
            ...group,
            events: group.events.sort((a, b) => {
                // 通配符事件排在前面
                if (a.value.includes('.*') && !b.value.includes('.*')) return -1;
                if (!a.value.includes('.*') && b.value.includes('.*')) return 1;
                return a.label.localeCompare(b.label);
            })
        }));
}

/**
 * 获取推荐的事件类型组合
 */
export function getRecommendedEventTypes(): Array<{
    name: string;
    description: string;
    events: string[];
}> {
    return [
        {
            name: '基础监控',
            description: '监控资源的创建、更新和删除',
            events: ['created', 'updated', 'deleted']
        },
        {
            name: 'Deployment 完整监控',
            description: '监控 Deployment 的所有事件',
            events: ['deployment.*']
        },
        {
            name: 'Pod 故障监控',
            description: '专注于 Pod 的故障和异常事件',
            events: ['pod.failed', 'pod.evicted', 'pod.oom_killed', 'pod.crash_loop_backoff', 'pod.image_pull_failed']
        },
        {
            name: '扩缩容监控',
            description: '监控资源的扩缩容事件',
            events: ['deployment.scaled', 'hpa.scaled_up', 'hpa.scaled_down']
        },
        {
            name: '服务可用性监控',
            description: '监控服务和端点的可用性',
            events: ['service.endpoint_ready', 'service.endpoint_not_ready', 'ingress.backend_error']
        },
        {
            name: '存储监控',
            description: '监控存储相关事件',
            events: ['pvc.*', 'pvc.resize_started', 'pvc.resize_completed']
        }
    ];
}

/**
 * 检查事件类型是否匹配过滤器
 */
export function eventTypeMatches(eventType: string, filters: string[]): boolean {
    if (filters.length === 0) return true;
    
    for (const filter of filters) {
        // 精确匹配
        if (filter === eventType) return true;
        
        // 通配符匹配
        if (filter.endsWith('.*')) {
            const prefix = filter.slice(0, -2);
            if (eventType.startsWith(prefix + '.')) return true;
        }
        
        // 动作匹配
        if (eventType.includes('.')) {
            const action = eventType.split('.').pop();
            if (action === filter) return true;
        }
    }
    
    return false;
}

/**
 * 获取事件类型的严重程度
 */
export function getEventSeverity(eventType: string): 'info' | 'warning' | 'error' {
    const errorEvents = [
        'pod.failed',
        'pod.evicted', 
        'pod.oom_killed',
        'pod.crash_loop_backoff',
        'pod.image_pull_failed',
        'deployment.rollout_failed',
        'service.endpoint_not_ready',
        'ingress.backend_error',
        'hpa.unable_to_scale'
    ];
    
    const warningEvents = [
        'pod.deleted',
        'deployment.deleted',
        'service.deleted',
        'pvc.pending',
        'deployment.paused'
    ];
    
    if (errorEvents.some(event => eventType === event || eventType.endsWith(event))) {
        return 'error';
    }
    
    if (warningEvents.some(event => eventType === event || eventType.endsWith(event))) {
        return 'warning';
    }
    
    return 'info';
}

/**
 * 获取事件类型的图标
 */
export function getEventIcon(eventType: string): string {
    if (eventType.startsWith('deployment.')) return '🚀';
    if (eventType.startsWith('pod.')) return '📦';
    if (eventType.startsWith('service.')) return '🔗';
    if (eventType.startsWith('ingress.')) return '🌐';
    if (eventType.startsWith('pvc.')) return '💾';
    if (eventType.startsWith('secret.')) return '🔐';
    if (eventType.startsWith('configmap.')) return '⚙️';
    if (eventType.startsWith('hpa.')) return '📊';
    
    // 通用事件
    if (eventType === 'created') return '✅';
    if (eventType === 'updated') return '🔄';
    if (eventType === 'deleted') return '🗑️';
    
    return '📋';
}
