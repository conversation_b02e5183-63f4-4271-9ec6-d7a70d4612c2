import type { EventTypeGroup } from '@/types/webhook';

/**
 * 事件类型的人类可读描述映射
 */
export const EVENT_TYPE_LABELS: Record<string, string> = {
    // 特殊事件类型
    'all': '所有事件',

    // Deployment 事件
    'deployment.created': 'Deployment 创建',
    'deployment.updated': 'Deployment 更新',
    'deployment.deleted': 'Deployment 删除',
    'deployment.scaled': 'Deployment 扩缩容',
    'deployment.rollout_started': 'Deployment 滚动更新开始',
    'deployment.rollout_completed': 'Deployment 滚动更新完成',
    'deployment.rollout_failed': 'Deployment 滚动更新失败',
    'deployment.paused': 'Deployment 暂停',
    'deployment.resumed': 'Deployment 恢复',

    // Pod 事件
    'pod.created': 'Pod 创建',
    'pod.started': 'Pod 启动',
    'pod.ready': 'Pod 就绪',
    'pod.failed': 'Pod 失败',
    'pod.succeeded': 'Pod 成功完成',
    'pod.deleted': 'Pod 删除',
    'pod.evicted': 'Pod 被驱逐',
    'pod.oom_killed': 'Pod 内存不足被杀死',
    'pod.image_pull_failed': 'Pod 镜像拉取失败',
    'pod.crash_loop_backoff': 'Pod 崩溃循环',

    // Service 事件
    'service.created': 'Service 创建',
    'service.updated': 'Service 更新',
    'service.deleted': 'Service 删除',
    'service.endpoint_ready': 'Service 端点就绪',
    'service.endpoint_not_ready': 'Service 端点不可用',

    // Ingress 事件
    'ingress.created': 'Ingress 创建',
    'ingress.updated': 'Ingress 更新',
    'ingress.deleted': 'Ingress 删除',
    'ingress.tls_configured': 'Ingress TLS 配置完成',
    'ingress.backend_error': 'Ingress 后端服务错误',

    // PVC 事件
    'pvc.created': 'PVC 创建',
    'pvc.bound': 'PVC 绑定成功',
    'pvc.pending': 'PVC 等待绑定',
    'pvc.deleted': 'PVC 删除',
    'pvc.resize_started': 'PVC 扩容开始',
    'pvc.resize_completed': 'PVC 扩容完成',

    // Secret 事件
    'secret.created': 'Secret 创建',
    'secret.updated': 'Secret 更新',
    'secret.deleted': 'Secret 删除',

    // ConfigMap 事件
    'configmap.created': 'ConfigMap 创建',
    'configmap.updated': 'ConfigMap 更新',
    'configmap.deleted': 'ConfigMap 删除',

    // HPA 事件
    'hpa.created': 'HPA 创建',
    'hpa.updated': 'HPA 更新',
    'hpa.deleted': 'HPA 删除',
    'hpa.scaled_up': 'HPA 扩容',
    'hpa.scaled_down': 'HPA 缩容',
    'hpa.unable_to_scale': 'HPA 无法扩缩容',

    // 通配符事件
    'deployment.*': 'Deployment 所有事件',
    'pod.*': 'Pod 所有事件',
    'service.*': 'Service 所有事件',
    'ingress.*': 'Ingress 所有事件',
    'pvc.*': 'PVC 所有事件',
    'secret.*': 'Secret 所有事件',
    'configmap.*': 'ConfigMap 所有事件',
    'hpa.*': 'HPA 所有事件',
};

/**
 * 获取事件类型的人类可读标签
 */
export function getEventTypeLabel(eventType: string): string {
    return EVENT_TYPE_LABELS[eventType] || eventType;
}

/**
 * 将事件类型按资源分组
 */
export function groupEventTypes(eventTypes: string[]): EventTypeGroup[] {
    const groups: Record<string, EventTypeGroup> = {};

    // 特殊事件组
    groups.special = {
        label: '特殊事件',
        events: []
    };

    // 资源特定事件组
    const resourceGroups: Record<string, string> = {
        deployment: 'Deployment 事件',
        pod: 'Pod 事件',
        service: 'Service 事件',
        ingress: 'Ingress 事件',
        pvc: 'PVC 事件',
        secret: 'Secret 事件',
        configmap: 'ConfigMap 事件',
        hpa: 'HPA 事件',
    };

    // 初始化资源组
    Object.entries(resourceGroups).forEach(([key, label]) => {
        groups[key] = {
            label,
            events: []
        };
    });

    // 分类事件类型
    eventTypes.forEach(eventType => {
        const event = {
            value: eventType,
            label: getEventTypeLabel(eventType)
        };

        // 特殊事件类型
        if (eventType === 'all') {
            groups.special.events.push({
                ...event,
                description: '监听所有事件类型'
            });
            return;
        }

        // 检查是否是通配符事件
        if (eventType.includes('.*')) {
            const resourceType = eventType.split('.')[0];
            if (groups[resourceType]) {
                groups[resourceType].events.push({
                    ...event,
                    description: `监听所有 ${resourceGroups[resourceType]}`
                });
            }
            return;
        }

        // 检查是否是资源特定事件
        if (eventType.includes('.')) {
            const resourceType = eventType.split('.')[0];
            if (groups[resourceType]) {
                groups[resourceType].events.push(event);
                return;
            }
        }
    });

    // 过滤掉空组并排序
    return Object.values(groups)
        .filter(group => group.events.length > 0)
        .map(group => ({
            ...group,
            events: group.events.sort((a, b) => {
                // 通配符事件排在前面
                if (a.value.includes('.*') && !b.value.includes('.*')) return -1;
                if (!a.value.includes('.*') && b.value.includes('.*')) return 1;
                return a.label.localeCompare(b.label);
            })
        }));
}

/**
 * 获取推荐的事件类型组合
 */
export function getRecommendedEventTypes(): Array<{
    name: string;
    description: string;
    events: string[];
}> {
    return [
        {
            name: '基础监控',
            description: '监控资源的创建、更新和删除',
            events: ['created', 'updated', 'deleted']
        },
        {
            name: 'Deployment 完整监控',
            description: '监控 Deployment 的所有事件',
            events: ['deployment.*']
        },
        {
            name: 'Pod 故障监控',
            description: '专注于 Pod 的故障和异常事件',
            events: ['pod.failed', 'pod.evicted', 'pod.oom_killed', 'pod.crash_loop_backoff', 'pod.image_pull_failed']
        },
        {
            name: '扩缩容监控',
            description: '监控资源的扩缩容事件',
            events: ['deployment.scaled', 'hpa.scaled_up', 'hpa.scaled_down']
        },
        {
            name: '服务可用性监控',
            description: '监控服务和端点的可用性',
            events: ['service.endpoint_ready', 'service.endpoint_not_ready', 'ingress.backend_error']
        },
        {
            name: '存储监控',
            description: '监控存储相关事件',
            events: ['pvc.*', 'pvc.resize_started', 'pvc.resize_completed']
        }
    ];
}

/**
 * 检查事件类型是否匹配过滤器
 */
export function eventTypeMatches(eventType: string, filters: string[]): boolean {
    if (filters.length === 0) return true;
    
    for (const filter of filters) {
        // 精确匹配
        if (filter === eventType) return true;
        
        // 通配符匹配
        if (filter.endsWith('.*')) {
            const prefix = filter.slice(0, -2);
            if (eventType.startsWith(prefix + '.')) return true;
        }
        
        // 动作匹配
        if (eventType.includes('.')) {
            const action = eventType.split('.').pop();
            if (action === filter) return true;
        }
    }
    
    return false;
}

/**
 * 获取事件类型的严重程度
 */
export function getEventSeverity(eventType: string): 'info' | 'warning' | 'error' {
    const errorEvents = [
        'pod.failed',
        'pod.evicted', 
        'pod.oom_killed',
        'pod.crash_loop_backoff',
        'pod.image_pull_failed',
        'deployment.rollout_failed',
        'service.endpoint_not_ready',
        'ingress.backend_error',
        'hpa.unable_to_scale'
    ];
    
    const warningEvents = [
        'pod.deleted',
        'deployment.deleted',
        'service.deleted',
        'pvc.pending',
        'deployment.paused'
    ];
    
    if (errorEvents.some(event => eventType === event || eventType.endsWith(event))) {
        return 'error';
    }
    
    if (warningEvents.some(event => eventType === event || eventType.endsWith(event))) {
        return 'warning';
    }
    
    return 'info';
}

/**
 * 获取事件类型的图标
 */
export function getEventIcon(eventType: string): string {
    if (eventType.startsWith('deployment.')) return '🚀';
    if (eventType.startsWith('pod.')) return '📦';
    if (eventType.startsWith('service.')) return '🔗';
    if (eventType.startsWith('ingress.')) return '🌐';
    if (eventType.startsWith('pvc.')) return '💾';
    if (eventType.startsWith('secret.')) return '🔐';
    if (eventType.startsWith('configmap.')) return '⚙️';
    if (eventType.startsWith('hpa.')) return '📊';
    
    // 通用事件
    if (eventType === 'created') return '✅';
    if (eventType === 'updated') return '🔄';
    if (eventType === 'deleted') return '🗑️';
    
    return '📋';
}
