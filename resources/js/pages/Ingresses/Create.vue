<template>
    <AppLayout>
        <Head title="创建入口" />

        <div class="p-4">
            <!-- Header Section -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">创建入口</h1>
                    <p class="mt-1 text-base text-gray-600 dark:text-gray-400">配置路由规则，将外部流量引导至您的服务</p>
                </div>
                <Button variant="outline" @click="router.visit(route('ingresses.index'))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回列表
                </Button>
            </div>

            <!-- Form Content -->
            <div class="space-y-10">
                <!-- Basic Configuration -->
                <section class="space-y-6">
                    <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">基本配置</h2>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">为您的新入口规则设置一个唯一的名称</p>
                    </div>

                    <div class="max-w-md">
                        <Label for="name" class="text-sm font-medium"> 名称 <span class="text-red-500">*</span> </Label>
                        <Input
                            id="name"
                            v-model="form.name"
                            placeholder="my-awesome-app-ingress"
                            :class="{ 'border-red-500': errors.name }"
                            class="mt-2"
                        />
                        <p v-if="errors.name" class="mt-2 text-sm text-red-600 dark:text-red-400">
                            {{ errors.name }}
                        </p>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">名称应为小写字母、数字和连字符组成，不能以连字符开头或结尾</p>
                    </div>
                </section>

                <!-- Routing Rules -->
                <section class="space-y-6">
                    <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">路由规则</h2>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">配置域名和路径，将外部请求路由到内部的指定服务和端口</p>
                            </div>
                            <Button type="button" variant="outline" @click="addRule">
                                <Plus class="mr-2 h-4 w-4" />
                                添加规则
                            </Button>
                        </div>
                    </div>

                    <div class="space-y-8">
                        <div
                            v-for="(rule, ruleIndex) in form.rules"
                            :key="ruleIndex"
                            class="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700"
                        >
                            <!-- Rule Header -->
                            <div class="border-b border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-800/50">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">规则 {{ ruleIndex + 1 }}</h3>
                                    <Button
                                        v-if="form.rules.length > 1"
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        @click="removeRule(ruleIndex)"
                                        class="text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:text-red-300"
                                    >
                                        <X class="mr-2 h-4 w-4" />
                                        移除规则
                                    </Button>
                                </div>
                            </div>

                            <!-- Rule Content -->
                            <div class="space-y-6 p-6">
                                <!-- Host Configuration -->
                                <div>
                                    <Label :for="`host-${ruleIndex}`" class="text-sm font-medium">
                                        域名 (Host) <span class="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        :id="`host-${ruleIndex}`"
                                        v-model="rule.host"
                                        placeholder="app.example.com"
                                        :class="{ 'border-red-500': errors[`rules.${ruleIndex}.host`] }"
                                        class="mt-2"
                                    />
                                    <p v-if="errors[`rules.${ruleIndex}.host`]" class="mt-2 text-sm text-red-600 dark:text-red-400">
                                        {{ errors[`rules.${ruleIndex}.host`] }}
                                    </p>
                                </div>

                                <!-- Paths Configuration -->
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <Label class="text-sm font-medium"> 路径配置 <span class="text-red-500">*</span> </Label>
                                        <Button type="button" size="sm" variant="outline" @click="addPath(ruleIndex)">
                                            <Plus class="mr-2 h-4 w-4" />
                                            添加路径
                                        </Button>
                                    </div>

                                    <div class="space-y-4">
                                        <div
                                            v-for="(path, pathIndex) in rule.http.paths"
                                            :key="pathIndex"
                                            class="space-y-4 rounded-lg bg-gray-50 p-5 dark:bg-gray-800/30"
                                        >
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">路径 {{ pathIndex + 1 }}</span>
                                                <Button
                                                    v-if="rule.http.paths.length > 1"
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    @click="removePath(ruleIndex, pathIndex)"
                                                    class="text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:text-red-300"
                                                >
                                                    <X class="h-4 w-4" />
                                                </Button>
                                            </div>

                                            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                <div>
                                                    <Label :for="`path-${ruleIndex}-${pathIndex}`" class="text-sm font-medium">路径</Label>
                                                    <Input :id="`path-${ruleIndex}-${pathIndex}`" v-model="path.path" placeholder="/" class="mt-1" />
                                                </div>

                                                <div>
                                                    <Label :for="`pathType-${ruleIndex}-${pathIndex}`" class="text-sm font-medium">路径类型</Label>
                                                    <Select v-model="path.pathType">
                                                        <SelectTrigger class="mt-1">
                                                            <SelectValue />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="Prefix">前缀 (Prefix)</SelectItem>
                                                            <SelectItem value="Exact">精确 (Exact)</SelectItem>
                                                            <SelectItem value="ImplementationSpecific">默认 (ImplementationSpecific)</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <p class="mt-1 text-xs text-gray-500">"默认"类型由底层驱动决定行为，通常等同于"前缀"</p>
                                                </div>
                                            </div>

                                            <div>
                                                <Label :for="`service-selector-${ruleIndex}-${pathIndex}`" class="text-sm font-medium">
                                                    后端服务 <span class="text-red-500">*</span>
                                                </Label>
                                                <div class="mt-1">
                                                    <ServiceSelector
                                                        :id="`service-selector-${ruleIndex}-${pathIndex}`"
                                                        v-model="path.backend.service"
                                                    />
                                                </div>
                                                <div class="mt-2 space-y-1">
                                                    <p
                                                        v-if="errors[`rules.${ruleIndex}.http.paths.${pathIndex}.backend.service.name`]"
                                                        class="text-sm text-red-600 dark:text-red-400"
                                                    >
                                                        {{ errors[`rules.${ruleIndex}.http.paths.${pathIndex}.backend.service.name`] }}
                                                    </p>
                                                    <p
                                                        v-if="errors[`rules.${ruleIndex}.http.paths.${pathIndex}.backend.service.port.number`]"
                                                        class="text-sm text-red-600 dark:text-red-400"
                                                    >
                                                        {{ errors[`rules.${ruleIndex}.http.paths.${pathIndex}.backend.service.port.number`] }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- TLS Configuration -->
                <section class="space-y-6">
                    <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
                        <div class="flex items-start justify-between">
                            <div>
                                <h2 class="flex items-center text-xl font-semibold text-gray-900 dark:text-white">
                                    <Shield class="mr-2 h-5 w-5" />
                                    TLS (HTTPS) 配置
                                </h2>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                    为您的域名启用 HTTPS 加密。您可以指定一个已存在的证书，或留空由系统自动为您生成
                                </p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Label for="enable-tls" class="text-sm font-medium">启用 TLS</Label>
                                <Switch id="enable-tls" v-model="enableTls" />
                            </div>
                        </div>
                    </div>

                    <div v-if="enableTls" class="space-y-6">
                        <div v-for="(tls, tlsIndex) in form.tls" :key="tlsIndex" class="rounded-lg border border-gray-200 p-6 dark:border-gray-700">
                            <div class="mb-4 flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">TLS 配置 {{ tlsIndex + 1 }}</h3>
                                <div class="flex space-x-2">
                                    <Button type="button" variant="outline" size="sm" @click="addTls">
                                        <Plus class="mr-2 h-4 w-4" />
                                        添加配置
                                    </Button>
                                    <Button
                                        v-if="form.tls && form.tls.length > 1"
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        @click="removeTls(tlsIndex)"
                                        class="text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:text-red-300"
                                    >
                                        <X class="mr-2 h-4 w-4" />
                                        移除配置
                                    </Button>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div>
                                    <Label :for="`tls-hosts-${tlsIndex}`" class="text-sm font-medium">
                                        域名列表 <span class="text-red-500">*</span>
                                    </Label>
                                    <div class="mt-1">
                                        <HostSelector
                                            v-model="tlsHosts[tlsIndex]"
                                            placeholder="app.example.com"
                                            add-button-text="添加域名"
                                            @update:model-value="(value) => updateTlsHosts(tlsIndex, value)"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <Label :for="`tls-secret-${tlsIndex}`" class="text-sm font-medium">TLS 证书</Label>
                                    <div class="mt-1">
                                        <SecretSelector
                                            v-model="tlsSecretNames[tlsIndex]"
                                            placeholder="选择 TLS 证书"
                                            secret-type="TLS"
                                            add-button-text="添加证书"
                                            @update:model-value="(value) => updateTlsSecretName(tlsIndex, value)"
                                        />
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">选择一个已存在的 TLS 证书，或留空让系统自动创建</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Labels Section -->
                <section class="space-y-6">
                    <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">自定义标签</h2>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">为入口规则添加自定义标签以便于管理和标识</p>
                    </div>

                    <div>
                        <LabelSelector v-model="form.labels" />
                    </div>
                </section>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4 border-t border-gray-200 pt-8 dark:border-gray-700">
                <Button variant="outline" @click="router.visit(route('ingresses.index'))"> 取消 </Button>
                <Button @click="createIngress" :disabled="creating" size="lg">
                    <Loader2 v-if="creating" class="mr-2 h-4 w-4 animate-spin" />
                    <Plus v-else class="mr-2 h-4 w-4" />
                    创建入口
                </Button>
            </div>
        </div>

        <!-- Debug Preview (development only) -->
        <div v-if="false" class="p-4">
            <DataPreview api="/api/ingresses" method="POST" :data-ref="form" />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import HostSelector from '@/components/selectors/HostSelector.vue';
import LabelSelector from '@/components/selectors/LabelSelector.vue';
import SecretSelector from '@/components/selectors/SecretSelector.vue';
import ServiceSelector from '@/components/selectors/ServiceSelector.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Switch from '@/components/ui/switch/Switch.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { CreateIngressData } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { ArrowLeft, Loader2, Plus, Shield, X } from 'lucide-vue-next';
import { toast } from 'sonner';
import { reactive, ref, watch } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    ingressClasses: string[];
}

const props = defineProps<Props>();

const creating = ref(false);
const enableTls = ref(false);
const errors = ref<Record<string, string>>({});

const form = reactive<CreateIngressData>({
    name: '',
    rules: [
        {
            host: '',
            http: {
                paths: [
                    {
                        path: '/',
                        pathType: 'Prefix',
                        backend: {
                            service: {
                                name: '',
                                port: {
                                    number: undefined,
                                },
                            },
                        },
                    },
                ],
            },
        },
    ],
    labels: {},
});

const tlsSecretNames = ref<string[][]>([]);
const tlsHosts = ref<string[][]>([]);

const addRule = () => {
    form.rules.push({
        host: '',
        http: {
            paths: [
                {
                    path: '/',
                    pathType: 'Prefix',
                    backend: {
                        service: {
                            name: '',
                            port: {
                                number: undefined,
                            },
                        },
                    },
                },
            ],
        },
    });
};

const removeRule = (index: number) => {
    form.rules.splice(index, 1);
};

const addPath = (ruleIndex: number) => {
    form.rules[ruleIndex].http.paths.push({
        path: '/',
        pathType: 'Prefix',
        backend: {
            service: {
                name: '',
                port: {
                    number: undefined,
                },
            },
        },
    });
};

const removePath = (ruleIndex: number, pathIndex: number) => {
    form.rules[ruleIndex].http.paths.splice(pathIndex, 1);
};

const addTls = () => {
    if (!form.tls) {
        form.tls = [];
    }
    form.tls.push({
        hosts: [],
        secretName: '',
    });
    tlsHosts.value.push([]);
    tlsSecretNames.value.push([]);
};

const removeTls = (index: number) => {
    if (form.tls) {
        form.tls.splice(index, 1);
        tlsHosts.value.splice(index, 1);
        tlsSecretNames.value.splice(index, 1);
    }
};

const updateTlsHosts = (index: number, hosts: string[]) => {
    if (form.tls && form.tls[index]) {
        // 过滤掉空的域名
        const filteredHosts = hosts.filter((h: string) => h.trim());
        form.tls[index].hosts = filteredHosts;
    }
};

const updateTlsSecretName = (index: number, secretNames: string[]) => {
    if (form.tls && form.tls[index]) {
        // 使用第一个选中的证书名称，如果没有选择则为空
        form.tls[index].secretName = secretNames.length > 0 ? secretNames[0] : '';
    }
};

// 监听 TLS 开关变化
watch(enableTls, (newVal) => {
    if (newVal) {
        if (!form.tls || form.tls.length === 0) {
            addTls();
        }
    } else {
        // 关闭 TLS 时，将 tls 数组清空
        if (form.tls) {
            form.tls.length = 0;
        }
        tlsHosts.value = [];
        tlsSecretNames.value = [];
    }
});

const createIngress = async () => {
    try {
        creating.value = true;
        errors.value = {};

        // 深拷贝以避免影响表单
        const finalForm: any = JSON.parse(JSON.stringify(form));

        // 如果启用了 TLS，整理 hosts 输入
        if (enableTls.value && finalForm.tls) {
            finalForm.tls.forEach((tls: any, index: number) => {
                // hosts已经通过HostSelector实时更新到form.tls中，无需额外处理
            });
        }

        // 若未启用 TLS，则确保不提交 tls 字段，或者提交一个空数组
        if (!enableTls.value) {
            finalForm.tls = [];
        }

        await axios.post('/api/ingresses', finalForm);

        toast.success('入口创建成功');
        router.visit(route('ingresses.index'));
    } catch (error: any) {
        console.error('Failed to create ingress:', error);

        if (error.response?.status === 422) {
            errors.value = error.response.data.errors || {};
            toast.error('表单验证失败，请检查输入');
        } else {
            toast.error(error.response?.data?.message || '创建入口失败');
        }
    } finally {
        creating.value = false;
    }
};
</script>

<style scoped>
.required::after {
    content: ' *';
    color: rgb(239 68 68);
}
</style>
