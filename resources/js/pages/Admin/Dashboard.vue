<template>
    <AdminLayout>
        <div class="container py-8">
            <div class="space-y-6">
                <div class="flex items-center justify-between">
                    <h1 class="text-3xl font-bold tracking-tight">管理员控制台</h1>
                    <Button @click="logout" variant="outline">
                        <LogOutIcon class="mr-2 h-4 w-4" />
                        退出登录
                    </Button>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import AdminLayout from '@/layouts/Admin.vue';
import { router } from '@inertiajs/vue3';
import { LogOutIcon } from 'lucide-vue-next';

const logout = () => {
    router.post(route('admin.logout'));
};
</script>
