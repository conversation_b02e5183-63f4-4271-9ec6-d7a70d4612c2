<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AuthBase from '@/layouts/AuthLayout.vue';
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import { Key, LoaderCircle, Shield } from 'lucide-vue-next';
import { ref } from 'vue';

defineProps<{
    status?: string;
    canResetPassword: boolean;
}>();

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const isPasskeyLoading = ref(false);
const passkeyError = ref('');

const submit = () => {
    form.post(route('login'), {
        onFinish: () => form.reset('password'),
    });
};

const loginWithPasskey = async () => {
    passkeyError.value = '';

    if (!window.browserSupportsWebAuthn || !window.browserSupportsWebAuthn()) {
        passkeyError.value = '您的浏览器不支持通行密钥功能';
        return;
    }

    try {
        isPasskeyLoading.value = true;

        // 获取认证选项
        const response = await fetch(window.route('passkeys.authentication_options'));
        if (!response.ok) {
            throw new Error('获取认证选项失败');
        }

        const options = await response.json();

        // 开始认证流程
        const authResult = await window.startAuthentication({ optionsJSON: options });

        // 提交认证结果
        router.post(window.route('passkeys.login'), {
            start_authentication_response: JSON.stringify(authResult),
        });
    } catch (error) {
        console.error('通行密钥登录失败:', error);
        passkeyError.value = '通行密钥登录失败，请重试';
    } finally {
        isPasskeyLoading.value = false;
    }
};
</script>

<template>
    <AuthBase title="登录您的账户" description="在下方输入您的邮箱和密码以登录">
        <Head title="登录" />

        <div v-if="status" class="mb-4 text-center text-sm font-medium text-green-600">
            {{ status }}
        </div>

        <form @submit.prevent="submit" class="flex flex-col gap-6">
            <div class="grid gap-6">
                <div class="grid gap-2">
                    <Label for="email">邮箱地址</Label>
                    <Input
                        id="email"
                        type="email"
                        required
                        autofocus
                        :tabindex="1"
                        autocomplete="email"
                        v-model="form.email"
                        placeholder="<EMAIL>"
                    />
                    <InputError :message="form.errors.email" />
                </div>

                <div class="grid gap-2">
                    <div class="flex items-center justify-between">
                        <Label for="password">密码</Label>
                        <TextLink v-if="canResetPassword" :href="route('password.request')" class="text-sm" :tabindex="5"> 忘记密码？ </TextLink>
                    </div>
                    <Input
                        id="password"
                        type="password"
                        required
                        :tabindex="2"
                        autocomplete="current-password"
                        v-model="form.password"
                        placeholder="密码"
                    />
                    <InputError :message="form.errors.password" />
                </div>

                <div class="flex items-center justify-between">
                    <Label for="remember" class="flex items-center space-x-3">
                        <Checkbox id="remember" v-model="form.remember" :tabindex="3" />
                        <span>记住我</span>
                    </Label>
                </div>

                <Button type="submit" class="mt-4 w-full" :tabindex="4" :disabled="form.processing">
                    <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin" />
                    登录
                </Button>
            </div>

            <!-- 分隔线 -->
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <Separator class="w-full" />
                </div>
                <div class="relative flex justify-center text-xs uppercase">
                    <span class="bg-background px-2 text-muted-foreground">或者</span>
                </div>
            </div>

            <!-- 通行密钥登录 -->
            <div class="space-y-4">
                <Button type="button" variant="outline" class="w-full" :disabled="isPasskeyLoading" @click="loginWithPasskey">
                    <Key class="mr-2 h-4 w-4" />
                    <LoaderCircle v-if="isPasskeyLoading" class="mr-2 h-4 w-4 animate-spin" />
                    {{ isPasskeyLoading ? '正在验证...' : '使用通行密钥登录' }}
                </Button>

                <!-- OTP 登录 -->
                <Button type="button" variant="outline" class="w-full" as-child>
                    <Link :href="route('otp.login')">
                        <Shield class="mr-2 h-4 w-4" />
                        使用 YubiKey OTP 登录
                    </Link>
                </Button>

                <!-- 通行密钥错误提示 -->
                <div v-if="passkeyError" class="text-center text-sm text-destructive">
                    {{ passkeyError }}
                </div>

                <!-- 登录方式说明 -->
                <div class="space-y-1 text-center text-xs text-muted-foreground">
                    <p>通行密钥：指纹、面容或设备密码登录</p>
                    <p>YubiKey OTP：硬件安全密钥登录</p>
                </div>
            </div>

            <div class="text-center text-sm text-muted-foreground">
                没有账户？
                <TextLink :href="route('register')" :tabindex="5">注册</TextLink>
            </div>
        </form>
    </AuthBase>
</template>
