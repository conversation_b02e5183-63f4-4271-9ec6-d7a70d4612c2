<script setup lang="ts">
import OTPLogin from '@/components/OTPLogin.vue';
import AuthBase from '@/layouts/AuthLayout.vue';
import { Head, Link } from '@inertiajs/vue3';

defineProps<{
    status?: string;
}>();
</script>

<template>
    <AuthBase>
        <Head title="Yubico OTP 登录" />

        <div v-if="status" class="mb-4 text-center text-sm font-medium text-green-600">
            {{ status }}
        </div>

        <OTPLogin :submit-route="route('otp.authenticate')" />

        <!-- 返回登录 -->
        <div class="mt-4 text-center">
            <Link :href="route('login')" class="text-sm text-muted-foreground"> 返回登录 </Link>
        </div>
    </AuthBase>
</template>
