<template>
    <AppLayout>
        <Head title="账户余额" />

        <div class="p-4 sm:p-6 lg:p-8">
            <p>目前正在测试阶段，不开放支付，完全免费，加Q群 439747955 获取兑换码。</p>

            <Card>
                <CardHeader class="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>账户余额</CardTitle>
                        <CardDescription>你的当前账户余额和充值记录，我们目前正在测试系统，无法在线充值，请通过兑换码充值。</CardDescription>
                    </div>
                    <div class="flex gap-2">
                        <RedeemCodeDialog />
                        <TopUpDialog />
                    </div>
                </CardHeader>
                <CardContent>
                    <div class="mb-6">
                        <p class="text-sm text-gray-500 dark:text-gray-400">当前余额</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">¥ {{ balance }}</p>
                    </div>
                    <p>平台按分钟计费时，含大小月，四舍五入精度的因素，每月价格会有所偏差。</p>

                    <h3 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">充值记录</h3>
                    <div class="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>流水号</TableHead>
                                    <TableHead>金额</TableHead>
                                    <TableHead>状态</TableHead>
                                    <TableHead>支付方式</TableHead>
                                    <TableHead>备注</TableHead>
                                    <TableHead>完成时间</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="record in records.data" :key="record.id">
                                    <TableCell class="font-mono">{{ record.transaction_number }}</TableCell>
                                    <TableCell>{{ formatAmount(record.amount) }}</TableCell>
                                    <TableCell>
                                        <Badge :variant="statusVariant(record.status)">
                                            {{ formatStatus(record.status) }}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>{{ formatPaymentMethod(record.payment_method) }}</TableCell>
                                    <TableCell>{{ record.remark || '-' }}</TableCell>
                                    <TableCell>{{ formatTime(record.completed_at) }}</TableCell>
                                </TableRow>
                                <TableRow v-if="records.data.length === 0">
                                    <TableCell colspan="6" class="text-center">没有充值记录</TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>

                    <Pagination :links="records.links" class="mt-6" />
                </CardContent>
            </Card>

            <!-- 计费记录 -->
            <Card class="mt-6" v-if="billingRecords && billingRecords.data.length > 0">
                <CardHeader>
                    <CardTitle>计费记录</CardTitle>
                    <CardDescription>最近的资源使用计费记录</CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>工作空间</TableHead>
                                    <TableHead>计费时间</TableHead>
                                    <TableHead>资源使用</TableHead>
                                    <TableHead>费用明细</TableHead>
                                    <TableHead>总费用</TableHead>
                                    <TableHead>状态</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="billing in billingRecords.data" :key="billing.id">
                                    <TableCell class="font-medium">{{
                                        billing.workspace_name ||
                                        `工作空间
                                        ${billing.workspace_id}`
                                    }}</TableCell>
                                    <TableCell>
                                        <div class="text-sm">
                                            <div>{{ formatTime(billing.billing_start_at) }}</div>
                                            <div class="text-gray-500">至 {{ formatTime(billing.billing_end_at) }}</div>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div class="space-y-1 text-sm">
                                            <div v-if="billing.resource_usage.memory_mi">内存: {{ billing.resource_usage.memory_mi }}Mi</div>
                                            <div v-if="billing.resource_usage.cpu_m">CPU: {{ billing.resource_usage.cpu_m }}m</div>
                                            <div v-if="billing.resource_usage.storage_gi">存储: {{ billing.resource_usage.storage_gi }}Gi</div>
                                            <div v-if="billing.resource_usage.loadbalancer_count">
                                                LoadBalancer: {{ billing.resource_usage.loadbalancer_count }}个
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div class="space-y-1 text-sm">
                                            <div v-if="parseFloat(billing.memory_cost) > 0">内存: {{ formatAmount(billing.memory_cost) }}</div>
                                            <div v-if="parseFloat(billing.cpu_cost) > 0">CPU: {{ formatAmount(billing.cpu_cost) }}</div>
                                            <div v-if="parseFloat(billing.storage_cost) > 0">存储: {{ formatAmount(billing.storage_cost) }}</div>
                                            <div v-if="parseFloat(billing.loadbalancer_cost) > 0">
                                                LB: {{ formatAmount(billing.loadbalancer_cost) }}
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell class="font-medium">{{ formatAmount(billing.total_cost) }}</TableCell>
                                    <TableCell>
                                        <Badge :variant="billingStatusVariant(billing.status)">
                                            {{ formatBillingStatus(billing.status) }}
                                        </Badge>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>

                    <Pagination :links="billingRecords.links" class="mt-6" v-if="billingRecords.links" />
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import Pagination from '@/components/Pagination.vue';
import RedeemCodeDialog from '@/components/RedeemCodeDialog.vue';
import TopUpDialog from '@/components/TopUpDialog.vue';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/AppLayout.vue';
import type { BillingRecord, Paginated, TopUpRecord } from '@/types/index';
import { Head } from '@inertiajs/vue3';
import { format } from 'date-fns';

interface Props {
    balance: string;
    records: Paginated<TopUpRecord>;
    billingRecords?: Paginated<BillingRecord>;
}

defineProps<Props>();

const formatAmount = (amount: number | string) => {
    return '¥ ' + Number(amount).toFixed(2);
};

const formatStatus = (status: string) => {
    const map: Record<string, string> = {
        pending: '待支付',
        completed: '已完成',
        failed: '失败',
        refunded: '已退款',
        partial_refunded: '部分退款',
    };
    return map[status] || status;
};

const statusVariant = (status: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
    const map: Record<string, 'default' | 'destructive' | 'outline' | 'secondary'> = {
        pending: 'outline',
        completed: 'default',
        failed: 'destructive',
        refunded: 'secondary',
        partial_refunded: 'secondary',
    };
    return map[status] || 'default';
};

const formatBillingStatus = (status: string) => {
    const map: Record<string, string> = {
        pending: '待处理',
        charged: '已扣费',
        failed: '扣费失败',
    };
    return map[status] || status;
};

const billingStatusVariant = (status: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
    const map: Record<string, 'default' | 'destructive' | 'outline' | 'secondary'> = {
        pending: 'outline',
        charged: 'default',
        failed: 'destructive',
    };
    return map[status] || 'default';
};

const formatPaymentMethod = (method: string) => {
    const map: Record<string, string> = {
        alipay: '支付宝',
        wechat: '微信支付',
        bank_card: '银行卡',
        manual: '手动充值',
        redeem_code: '兑换码',
        test_payment: '测试支付',
    };
    return map[method] || method;
};

const formatTime = (time: string | null) => {
    return time ? format(new Date(time), 'yyyy-MM-dd HH:mm:ss') : '-';
};
</script>
