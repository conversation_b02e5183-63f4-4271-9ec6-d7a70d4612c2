<template>
    <div class="min-h-screen bg-background">
        <Head :title="`${t('welcome.title')}`" />

        <!-- 导航栏 -->
        <nav class="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex h-16 items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <AppLogoIcon class="h-8 w-8" />
                        <span class="text-xl font-bold">{{ page.props.display_name }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- 语言切换 -->
                        <LanguageToggle />

                        <!-- 桌面菜单 -->
                        <div class="hidden items-center space-x-2 md:flex">
                            <a :href="route('login')">
                                <Button variant="ghost"> {{ t('welcome.nav.login') }} </Button>
                            </a>
                            <a :href="route('register')">
                                <Button> {{ t('welcome.nav.register') }} </Button>
                            </a>
                        </div>

                        <!-- 移动端菜单按钮 -->
                        <div class="flex items-center md:hidden">
                            <Button variant="ghost" size="icon" @click="isMobileMenuOpen = !isMobileMenuOpen">
                                <Menu class="h-6 w-6" />
                                <span class="sr-only">Open main menu</span>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 移动端展开菜单 -->
            <div v-if="isMobileMenuOpen" class="border-t bg-background md:hidden">
                <div class="space-y-1 px-4 pt-2 pb-3">
                    <a :href="route('login')" class="block rounded-md px-3 py-2 text-base font-medium text-foreground hover:bg-muted">{{
                        t('welcome.nav.login')
                    }}</a>
                    <a :href="route('register')" class="block rounded-md px-3 py-2 text-base font-medium text-foreground hover:bg-muted">{{
                        t('welcome.nav.register')
                    }}</a>
                </div>
            </div>
        </nav>

        <!-- 英雄区域 -->
        <section class="relative overflow-hidden bg-background py-20 sm:py-24">
            <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
            <div class="absolute inset-0 bg-gradient-to-tr from-accent/3 via-transparent to-muted/3" />
            <div class="relative container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-4xl text-center">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-5xl lg:text-6xl">
                        {{ t('welcome.hero.title') }}
                        <br />
                        <span class="text-primary">
                            <span class="typewriter-container inline-block w-[240px] text-center sm:w-[320px]">
                                <span class="typewriter-text">{{ currentApp }}</span>
                                <span class="typewriter-cursor">|</span>
                            </span>
                        </span>
                    </h1>
                    <p class="mx-auto mt-6 max-w-3xl text-base leading-7 text-muted-foreground sm:text-lg sm:leading-8">
                        {{ t('welcome.hero.subtitle') }}
                    </p>
                    <div class="mt-8 flex flex-wrap justify-center gap-2 text-sm sm:gap-4">
                        <div class="flex items-center space-x-2 rounded-full bg-primary/10 px-3 py-1.5 sm:px-4 sm:py-2">
                            <CheckCircle class="h-4 w-4 text-primary" />
                            <span>{{ t('welcome.hero.feature1') }}</span>
                        </div>
                        <div class="flex items-center space-x-2 rounded-full bg-primary/10 px-3 py-1.5 sm:px-4 sm:py-2">
                            <CheckCircle class="h-4 w-4 text-primary" />
                            <span>{{ t('welcome.hero.feature2') }}</span>
                        </div>
                        <div class="flex items-center space-x-2 rounded-full bg-primary/10 px-3 py-1.5 sm:px-4 sm:py-2">
                            <CheckCircle class="h-4 w-4 text-primary" />
                            <span>{{ t('welcome.hero.feature3') }}</span>
                        </div>
                    </div>
                    <div class="mt-10 flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-x-6">
                        <a :href="route('register')">
                            <Button size="lg">
                                <Rocket class="mr-2 h-5 w-5" />
                                {{ t('welcome.hero.buttons.start') }}
                            </Button>
                        </a>
                        <Button variant="outline" size="lg" @click="scrollToPricingComparison">
                            <Calculator class="mr-2 h-5 w-5" />
                            智能价格对比
                        </Button>
                        <Button variant="outline" size="lg" @click="scrollToFeatures">
                            <Play class="mr-2 h-5 w-5" />
                            {{ t('welcome.hero.buttons.learn') }}
                        </Button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特性展示 -->
        <section id="features" class="py-20 sm:py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-3xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">{{ t('welcome.comparison.title') }}</h2>
                    <p class="mt-4 text-base text-muted-foreground sm:text-lg">{{ t('welcome.comparison.subtitle') }}</p>
                    <div class="mt-8 grid grid-cols-1 gap-6 text-sm md:grid-cols-3">
                        <div class="rounded-lg border border-destructive/20 bg-destructive/10 p-4">
                            <h4 class="mb-2 font-semibold text-destructive">{{ t('welcome.comparison.traditional.title') }}</h4>
                            <ul class="space-y-1 text-left text-muted-foreground">
                                <li>• {{ t('welcome.comparison.traditional.feature1') }}</li>
                                <li>• {{ t('welcome.comparison.traditional.feature2') }}</li>
                                <li>• {{ t('welcome.comparison.traditional.feature3') }}</li>
                                <li>• {{ t('welcome.comparison.traditional.feature4') }}</li>
                            </ul>
                        </div>
                        <div class="flex items-center justify-center">
                            <ArrowRight class="hidden h-8 w-8 text-primary md:block" />
                            <div class="block w-full py-4 text-center md:hidden">
                                <ArrowRight class="mx-auto h-6 w-6 rotate-90 text-primary" />
                            </div>
                        </div>
                        <div class="rounded-lg border border-primary/20 bg-primary/10 p-4">
                            <h4 class="mb-2 font-semibold text-primary">{{ t('welcome.comparison.modern.title') }}</h4>
                            <ul class="space-y-1 text-left text-muted-foreground">
                                <li>• {{ t('welcome.comparison.modern.feature1') }}</li>
                                <li>• {{ t('welcome.comparison.modern.feature2') }}</li>
                                <li>• {{ t('welcome.comparison.modern.feature3') }}</li>
                                <li>• {{ t('welcome.comparison.modern.feature4') }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="mx-auto mt-16 max-w-7xl">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 transition-colors duration-300 group-hover:bg-primary/20"
                                    >
                                        <Zap class="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.features.autoScale.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.features.autoScale.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 transition-colors duration-300 group-hover:bg-primary/20"
                                    >
                                        <Shield class="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.features.selfHealing.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.features.selfHealing.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 transition-colors duration-300 group-hover:bg-primary/20"
                                    >
                                        <RefreshCw class="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.features.rollingUpdate.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.features.rollingUpdate.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 transition-colors duration-300 group-hover:bg-primary/20"
                                    >
                                        <Network class="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.features.loadBalancer.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.features.loadBalancer.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 transition-colors duration-300 group-hover:bg-primary/20"
                                    >
                                        <HardDrive class="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.features.clusterStorage.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.features.clusterStorage.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 transition-colors duration-300 group-hover:bg-primary/20"
                                    >
                                        <FileText class="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.features.manifestDeploy.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.features.manifestDeploy.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 transition-colors duration-300 group-hover:bg-primary/20"
                                    >
                                        <Layers class="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.features.highAvailability.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.features.highAvailability.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 transition-colors duration-300 group-hover:bg-primary/20"
                                    >
                                        <DollarSign class="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.features.elasticBilling.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.features.elasticBilling.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </section>

        <!-- AI 助手功能介绍 -->
        <section id="ai-assistant" class="py-20 sm:py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-3xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                        {{ t('welcome.aiAssistant.title') }}
                        <span class="text-primary">{{ t('welcome.aiAssistant.titleHighlight') }}</span>
                    </h2>
                    <p class="mt-4 text-base text-muted-foreground sm:text-lg">
                        {{ t('welcome.aiAssistant.subtitle') }}
                    </p>
                </div>

                <!-- AI 功能展示 -->
                <div class="mx-auto mt-16 max-w-7xl">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <MessageSquare
                                            class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110"
                                        />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.features.naturalLanguage.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">
                                        {{ t('welcome.aiAssistant.features.naturalLanguage.description') }}
                                        <br />
                                        <span class="mt-2 inline-block rounded-lg bg-muted px-3 py-1 font-mono text-xs text-foreground sm:text-sm">
                                            {{ t('welcome.aiAssistant.features.naturalLanguage.example') }}
                                        </span>
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <MousePointer class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.features.webOperation.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.aiAssistant.features.webOperation.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <Brain class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.features.contextUnderstanding.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">
                                        {{ t('welcome.aiAssistant.features.contextUnderstanding.description') }}
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <Lightbulb class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h3 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.features.smartSuggestions.title') }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.aiAssistant.features.smartSuggestions.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                <!-- AI 演示区域 -->
                <div class="mx-auto mt-16 max-w-4xl">
                    <Card class="overflow-hidden border-primary/20 bg-primary/5">
                        <CardContent class="p-4 sm:p-6 lg:p-8">
                            <div class="grid gap-8 lg:grid-cols-2">
                                <!-- 左侧：对话演示 -->
                                <div>
                                    <div class="mb-4 flex items-center space-x-2">
                                        <div class="h-3 w-3 rounded-full bg-red-400"></div>
                                        <div class="h-3 w-3 rounded-full bg-yellow-400"></div>
                                        <div class="h-3 w-3 rounded-full bg-green-400"></div>
                                        <span class="ml-4 text-sm font-medium text-foreground">{{ t('welcome.aiAssistant.demo.title') }}</span>
                                    </div>

                                    <div class="space-y-4">
                                        <div class="flex items-start space-x-3">
                                            <div class="flex h-8 w-8 items-center justify-center rounded-full border bg-background">
                                                <User class="h-4 w-4 text-muted-foreground" />
                                            </div>
                                            <div class="rounded-lg border bg-background px-3 py-2 shadow-sm">
                                                <p class="text-sm font-medium">{{ t('welcome.aiAssistant.demo.userMessage') }}</p>
                                            </div>
                                        </div>

                                        <div class="flex items-start space-x-3">
                                            <div class="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                                                <Sparkles class="h-4 w-4" />
                                            </div>
                                            <div class="flex-1 rounded-lg border border-primary/20 bg-primary/10 px-3 py-2">
                                                <p class="text-sm font-medium">{{ t('welcome.aiAssistant.demo.aiResponse') }}</p>
                                                <ul class="mt-2 text-xs text-muted-foreground">
                                                    <li v-for="step in tm('welcome.aiAssistant.demo.steps')" :key="step">• {{ rt(step) }}</li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="flex items-center space-x-2 text-xs text-muted-foreground">
                                            <LoaderCircle class="h-3 w-3 animate-spin text-primary" />
                                            <span>{{ t('welcome.aiAssistant.demo.processing') }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧：操作步骤指示器 -->
                                <div class="space-y-4">
                                    <h4 class="text-sm font-medium text-foreground">{{ t('welcome.aiAssistant.progress') || '执行进度' }}</h4>
                                    <div class="space-y-2">
                                        <div
                                            v-for="step in tm('welcome.aiAssistant.demo.completed')"
                                            :key="step"
                                            class="flex items-center space-x-2 text-xs"
                                        >
                                            <CheckCircle class="h-4 w-4 text-green-600" />
                                            <span class="text-muted-foreground">{{ rt(step) }}</span>
                                        </div>
                                        <div class="flex items-center space-x-2 text-xs">
                                            <LoaderCircle class="h-4 w-4 animate-spin text-primary" />
                                            <span class="text-muted-foreground">{{ t('welcome.aiAssistant.demo.current') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <!-- 使用场景示例 -->
                <div class="mx-auto mt-16 max-w-6xl">
                    <div class="mx-auto max-w-3xl text-center">
                        <h3 class="text-2xl font-bold text-foreground">{{ t('welcome.aiAssistant.useCases.title') }}</h3>
                        <p class="mt-2 text-base text-muted-foreground sm:text-lg">{{ t('welcome.aiAssistant.useCases.subtitle') }}</p>
                    </div>

                    <div class="mt-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <Globe class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h4 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.useCases.quickWebsite.title') }}</h4>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.aiAssistant.useCases.quickWebsite.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <Database class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h4 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.useCases.databaseDeploy.title') }}</h4>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.aiAssistant.useCases.databaseDeploy.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <Settings class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h4 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.useCases.configOptimization.title') }}</h4>
                                    <p class="text-sm text-muted-foreground">
                                        {{ t('welcome.aiAssistant.useCases.configOptimization.description') }}
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <AlertTriangle
                                            class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110"
                                        />
                                    </div>
                                    <h4 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.useCases.troubleshooting.title') }}</h4>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.aiAssistant.useCases.troubleshooting.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <Shield class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h4 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.useCases.securityConfig.title') }}</h4>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.aiAssistant.useCases.securityConfig.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card class="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                            <CardContent class="p-4 sm:p-6">
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted transition-colors duration-300 group-hover:bg-muted/80"
                                    >
                                        <Layers class="h-6 w-6 text-muted-foreground transition-transform duration-300 group-hover:scale-110" />
                                    </div>
                                    <h4 class="mb-2 text-lg font-semibold">{{ t('welcome.aiAssistant.useCases.microservices.title') }}</h4>
                                    <p class="text-sm text-muted-foreground">{{ t('welcome.aiAssistant.useCases.microservices.description') }}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                <!-- CTA 按钮 -->
                <div class="mt-16 text-center">
                    <div class="inline-flex flex-col items-center gap-4 sm:flex-row sm:gap-4">
                        <a :href="route('register')">
                            <Button size="lg">
                                <Rocket class="mr-2 h-5 w-5" />
                                {{ t('welcome.aiAssistant.cta.experience') }}
                            </Button>
                        </a>
                        <Button variant="outline" size="lg" @click="scrollToFeatures">
                            <Play class="mr-2 h-5 w-5" />
                            {{ t('welcome.aiAssistant.cta.learnMore') }}
                        </Button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 支持的应用 -->
        <section class="bg-muted/50 py-20 sm:py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-3xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">{{ t('welcome.applications.title') }}</h2>
                    <p class="mt-4 text-base text-muted-foreground sm:text-lg">{{ t('welcome.applications.subtitle') }}</p>
                    <div class="mt-6 flex flex-wrap justify-center gap-x-2 gap-y-2 text-sm text-muted-foreground">
                        <span class="rounded-full bg-primary/10 px-3 py-1">{{ t('welcome.applications.tag1') }}</span>
                        <span class="mx-2 hidden sm:inline">•</span>
                        <span class="rounded-full bg-primary/10 px-3 py-1">{{ t('welcome.applications.tag2') }}</span>
                        <span class="mx-2 hidden sm:inline">•</span>
                        <span class="rounded-full bg-primary/10 px-3 py-1">{{ t('welcome.applications.tag3') }}</span>
                    </div>
                </div>
                <div class="mx-auto mt-16 grid max-w-6xl grid-cols-2 gap-4 sm:gap-6 md:grid-cols-3 lg:grid-cols-6">
                    <div
                        v-for="(app, index) in supportedApps"
                        :key="app.name"
                        class="app-card group relative flex cursor-pointer flex-col items-center justify-center rounded-lg bg-background p-4 shadow-sm transition-all duration-200 hover:scale-105 hover:shadow-md sm:p-6"
                        :style="{ animationDelay: `${index * 100}ms` }"
                    >
                        <component
                            :is="app.icon"
                            class="mb-3 h-10 w-10 text-primary transition-transform duration-200 group-hover:scale-110 sm:h-12 sm:w-12"
                        />
                        <span class="text-center text-sm font-medium">{{ app.name }}</span>
                        <span class="mt-1 text-center text-xs text-muted-foreground">{{ app.description }}</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 成本对比 -->
        <PricingComparison />

        <!-- 价格计算器 -->
        <section id="pricing-calculator" class="hidden py-20 sm:py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-2xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">{{ t('welcome.calculator.title') }}</h2>
                    <p class="mt-4 text-base text-muted-foreground sm:text-lg">
                        {{ t('welcome.calculator.subtitle') }}
                        <br />
                        <small class="text-xs">{{ t('welcome.calculator.note') }}</small>
                    </p>
                </div>

                <!-- 集群选择 -->
                <div class="mx-auto mt-12 max-w-4xl">
                    <div class="mb-8">
                        <label class="mb-3 block text-sm font-medium">{{ t('welcome.calculator.selectCluster') }}</label>
                        <div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                            <Card
                                v-for="cluster in availableClusters"
                                :key="cluster.id"
                                :class="[
                                    'cursor-pointer border-2 transition-all duration-200',
                                    selectedCluster?.id === cluster.id ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50',
                                ]"
                                @click="selectCluster(cluster)"
                            >
                                <CardContent class="p-4">
                                    <div class="flex items-center space-x-3">
                                        <Server class="h-6 w-6 text-primary" />
                                        <div>
                                            <h3 class="font-semibold">{{ cluster.name }}</h3>
                                            <p class="text-sm text-muted-foreground">{{ cluster.description || '高性能集群' }}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>

                    <!-- 资源配置 -->
                    <div v-if="selectedCluster" class="grid gap-8 lg:grid-cols-2">
                        <!-- 左侧：资源配置 -->
                        <div class="space-y-6">
                            <Card>
                                <CardContent class="p-4 sm:p-6">
                                    <h3 class="mb-6 text-lg font-semibold">{{ t('welcome.calculator.resourceConfig') }}</h3>

                                    <!-- 内存配置 -->
                                    <div class="mb-6">
                                        <label class="mb-2 block text-sm font-medium">{{ t('welcome.calculator.memory') }}</label>
                                        <Input
                                            v-model.number="resources.memory"
                                            type="number"
                                            :min="512"
                                            :max="1048576"
                                            :step="512"
                                            placeholder="512"
                                            class="w-full"
                                        />
                                        <p class="mt-1 text-xs text-muted-foreground">{{ t('welcome.calculator.memoryNote') }}</p>
                                    </div>

                                    <!-- CPU配置 -->
                                    <div class="mb-6">
                                        <label class="mb-2 block text-sm font-medium">{{ t('welcome.calculator.cpu') }}</label>
                                        <Input
                                            v-model.number="resources.cpu"
                                            type="number"
                                            :min="500"
                                            :max="1000000"
                                            :step="500"
                                            placeholder="500"
                                            class="w-full"
                                        />
                                        <p class="mt-1 text-xs text-muted-foreground">{{ t('welcome.calculator.cpuNote') }}</p>
                                    </div>

                                    <!-- 存储配置 -->
                                    <div class="mb-6">
                                        <label class="mb-2 block text-sm font-medium">{{ t('welcome.calculator.storage') }}</label>
                                        <Input
                                            v-model.number="resources.storage"
                                            type="number"
                                            :min="0"
                                            :max="10240"
                                            placeholder="0"
                                            class="w-full"
                                        />
                                        <p class="mt-1 text-xs text-muted-foreground">{{ t('welcome.calculator.storageNote') }}</p>
                                    </div>

                                    <!-- 预设配置 -->
                                    <div>
                                        <label class="mb-3 block text-sm font-medium">{{ t('welcome.calculator.quickConfig') }}</label>
                                        <div class="grid gap-2 sm:grid-cols-3">
                                            <Button variant="outline" size="sm" @click="applyPreset('light')" class="text-xs">
                                                {{ t('welcome.calculator.lightApp') }}
                                            </Button>
                                            <Button variant="outline" size="sm" @click="applyPreset('standard')" class="text-xs">
                                                {{ t('welcome.calculator.standardApp') }}
                                            </Button>
                                            <Button variant="outline" size="sm" @click="applyPreset('performance')" class="text-xs">
                                                {{ t('welcome.calculator.performanceApp') }}
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        <!-- 右侧：价格预览 -->
                        <div>
                            <Card>
                                <CardContent class="p-4 sm:p-6">
                                    <h3 class="mb-6 text-lg font-semibold">{{ t('welcome.calculator.pricePreview') }}</h3>

                                    <div v-if="isCalculating" class="flex items-center justify-center py-8">
                                        <LoaderCircle class="h-6 w-6 animate-spin text-primary" />
                                        <span class="ml-2 text-sm text-muted-foreground">{{ t('common.loading') }}</span>
                                    </div>

                                    <div v-else-if="priceResult" class="space-y-4">
                                        <!-- 费用明细 -->
                                        <div class="space-y-3">
                                            <div v-if="resources.memory > 0" class="flex justify-between text-sm">
                                                <span class="flex items-center">
                                                    <MemoryStick class="mr-2 h-4 w-4 text-muted-foreground" />
                                                    {{ t('dashboard.charts.memory') }}费用
                                                </span>
                                                <span>{{ priceResult.breakdown.memory_price_per_minute }}/分钟</span>
                                            </div>
                                            <div v-if="resources.cpu > 0" class="flex justify-between text-sm">
                                                <span class="flex items-center">
                                                    <Cpu class="mr-2 h-4 w-4 text-muted-foreground" />
                                                    {{ t('dashboard.charts.cpu') }}费用
                                                </span>
                                                <span>{{ priceResult.breakdown.cpu_price_per_minute }}/分钟</span>
                                            </div>
                                            <div v-if="resources.storage > 0" class="flex justify-between text-sm">
                                                <span class="flex items-center">
                                                    <HardDrive class="mr-2 h-4 w-4 text-muted-foreground" />
                                                    {{ t('dashboard.charts.storage') }}费用
                                                </span>
                                                <span>{{ priceResult.breakdown.storage_price_per_minute }}/分钟</span>
                                            </div>
                                        </div>

                                        <hr class="border-border" />

                                        <!-- 总费用 -->
                                        <div class="space-y-2">
                                            <div class="flex justify-between font-semibold">
                                                <span>每分钟</span>
                                                <span class="text-primary">{{ priceResult.formatted.per_minute }}</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span>每小时</span>
                                                <span>{{ priceResult.formatted.per_hour }}</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span>每天</span>
                                                <span>{{ priceResult.formatted.per_day }}</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span>每月</span>
                                                <span>{{ priceResult.formatted.per_month }}</span>
                                            </div>
                                        </div>

                                        <!-- 成本优化建议 -->
                                        <div v-if="optimizationTips.length > 0" class="mt-6 rounded-lg bg-blue-50 p-4 dark:bg-blue-950/20">
                                            <h4 class="mb-2 text-sm font-medium text-blue-900 dark:text-blue-100">💡 成本优化建议</h4>
                                            <ul class="space-y-1 text-xs text-blue-800 dark:text-blue-200">
                                                <li v-for="tip in optimizationTips" :key="tip">• {{ tip }}</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div v-else-if="calculationError" class="rounded-lg bg-destructive/10 p-4 text-center">
                                        <p class="text-sm text-destructive">{{ calculationError }}</p>
                                    </div>

                                    <div v-else class="py-8 text-center text-sm text-muted-foreground">{{ t('welcome.calculator.configFirst') }}</div>

                                    <!-- 开始使用按钮 -->
                                    <div class="mt-6 border-t pt-4">
                                        <a :href="route('register')">
                                            <Button size="lg" variant="secondary">
                                                <UserPlus class="mr-2 h-5 w-5" />
                                                {{ t('welcome.calculator.register') }}
                                            </Button>
                                        </a>

                                        <p class="mt-2 text-center text-xs text-muted-foreground">{{ t('welcome.calculator.registerNote') }}</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 价格历史趋势 -->
        <section id="pricing-history" class="bg-primary/5 py-20 sm:py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-2xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">价格变动趋势</h2>
                    <p class="mt-4 text-base text-muted-foreground sm:text-lg">
                        实时监控集群价格变化，透明的定价历史让您做出明智的选择
                        <br />
                        <small class="text-xs">价格数据每次变动时自动记录，为您提供完整的价格历史</small>
                    </p>
                </div>

                <div class="mx-auto mt-12 max-w-6xl">
                    <PricingHistoryChart :selected-cluster="selectedCluster" />
                </div>
            </div>
        </section>

        <!-- 信任建立 -->
        <section class="hidden py-16 sm:py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-4xl text-center">
                    <h2 class="mb-12 text-2xl font-bold tracking-tight text-foreground sm:text-3xl">专业可靠，值得信赖</h2>
                    <div class="grid grid-cols-1 gap-8 md:grid-cols-3">
                        <div class="text-center">
                            <div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                                <Server class="h-8 w-8 text-primary" />
                            </div>
                            <h3 class="mb-2 font-semibold">99.9% 可用性保障</h3>
                            <p class="text-sm text-muted-foreground">多节点冗余部署，确保服务高可用</p>
                        </div>
                        <div class="text-center">
                            <div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                                <Shield class="h-8 w-8 text-primary" />
                            </div>
                            <h3 class="mb-2 font-semibold">数据安全保护</h3>
                            <p class="text-sm text-muted-foreground">企业级安全防护，数据加密存储</p>
                        </div>
                        <div class="text-center">
                            <div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                                <Clock class="h-8 w-8 text-primary" />
                            </div>
                            <h3 class="mb-2 font-semibold">7x24 技术支持</h3>
                            <p class="text-sm text-muted-foreground">专业技术团队，随时为您服务</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 部分 -->
        <section class="bg-primary py-16 sm:py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-3xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">{{ t('welcome.cta.title') }}</h2>
                    <p class="mt-4 text-base text-primary-foreground/90 sm:text-lg">
                        {{
                            t('welcome.cta.subtitle', {
                                name: page.props.display_name,
                            })
                        }}
                    </p>
                    <div class="mt-6 flex flex-wrap justify-center gap-4 text-sm text-primary-foreground/80">
                        <div class="flex items-center space-x-2">
                            <CheckCircle class="h-4 w-4" />
                            <span>{{ t('welcome.cta.feature1') }}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <CheckCircle class="h-4 w-4" />
                            <span>{{ t('welcome.cta.feature2') }}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <CheckCircle class="h-4 w-4" />
                            <span>{{ t('welcome.cta.feature3') }}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <CheckCircle class="h-4 w-4" />
                            <span>{{ t('welcome.cta.feature4') }}</span>
                        </div>
                    </div>
                    <div class="mt-8 flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-x-6">
                        <a :href="route('register')">
                            <Button size="lg" variant="secondary">
                                <UserPlus class="mr-2 h-5 w-5" />
                                {{ t('welcome.cta.startFree') }}
                            </Button>
                        </a>
                        <a :href="route('login')">
                            <Button size="lg" variant="secondary">
                                <LogIn class="mr-2 h-5 w-5" />
                                {{ t('welcome.cta.login') }}
                            </Button>
                        </a>
                    </div>
                    <p class="mt-4 text-xs text-primary-foreground/60">{{ t('welcome.cta.note') }}</p>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="border-t bg-background py-12">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col items-center justify-between space-y-4 sm:flex-row sm:space-y-0">
                    <div class="flex items-center space-x-4">
                        <AppLogoIcon class="h-6 w-6" />
                        <div class="text-sm text-muted-foreground">
                            <div>
                                {{ new Date().getFullYear() }}
                                中山市利飞科技有限公司
                            </div>
                            <div class="mt-1">
                                <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2025440018号</a>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-6 text-sm text-muted-foreground">
                        <a href="#" class="transition-colors hover:text-foreground">{{ t('welcome.footer.privacy') }}</a>
                        <a href="#" class="transition-colors hover:text-foreground">{{ t('welcome.footer.terms') }}</a>
                        <a href="#" class="transition-colors hover:text-foreground">{{ t('welcome.footer.contact') }}</a>
                    </div>
                </div>
            </div>
        </footer>

        <!-- 返回顶部按钮 -->
        <Transition name="fade">
            <Button v-if="showBackToTop" @click="scrollToTop" class="fixed right-8 bottom-8 z-50 shadow-lg" size="icon" aria-label="返回顶部">
                <ChevronUp class="h-5 w-5" />
            </Button>
        </Transition>
    </div>
</template>

<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import LanguageToggle from '@/components/LanguageToggle.vue';
import PricingComparison from '@/components/PricingComparison.vue';
import PricingHistoryChart from '@/components/PricingHistoryChart.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useI18n } from '@/composables/useI18n';
import axios from '@/lib/axios';
import type { PriceCalculationResponse } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';
import {
    AlertTriangle,
    ArrowRight,
    Brain,
    Calculator,
    CheckCircle,
    ChevronUp,
    Clock,
    Cpu,
    Database,
    DollarSign,
    FileText,
    Globe,
    HardDrive,
    Layers,
    Lightbulb,
    LoaderCircle,
    LogIn,
    MemoryStick,
    Menu,
    MessageSquare,
    MousePointer,
    Network,
    Play,
    RefreshCw,
    Rocket,
    Server,
    Settings,
    Shield,
    Sparkles,
    User,
    UserPlus,
    Zap,
} from 'lucide-vue-next';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { route } from 'ziggy-js';

const page = usePage();
const { t, rt, tm, initLocale } = useI18n();

const isMobileMenuOpen = ref(false);

// 价格计算器相关状态
const availableClusters = ref<Array<{ id: number; name: string; description?: string }>>([]);
const selectedCluster = ref<{ id: number; name: string; description?: string } | null>(null);
const resources = ref({
    memory: 512,
    cpu: 500,
    storage: 0,
});
const priceResult = ref<PriceCalculationResponse | null>(null);
const isCalculating = ref(false);
const calculationError = ref('');

// 获取集群列表
const fetchClusters = async () => {
    try {
        const response = await axios.get('/api/clusters');
        const clusters = response.data.filter((cluster: any) => cluster.billing_enabled);
        availableClusters.value = clusters;
        if (clusters.length > 0) {
            selectedCluster.value = clusters[0];
        }
    } catch (error) {
        console.error('Failed to fetch clusters:', error);
    }
};

// 选择集群
const selectCluster = (cluster: { id: number; name: string; description?: string }) => {
    selectedCluster.value = cluster;
    calculatePrice();
};

// 计算价格
const calculatePrice = async () => {
    if (!selectedCluster.value) return;

    // 检查是否有资源配置
    if (resources.value.memory === 0 && resources.value.cpu === 0 && resources.value.storage === 0) {
        priceResult.value = null;
        return;
    }

    isCalculating.value = true;
    calculationError.value = '';

    try {
        const response = await axios.post(`/api/pricing/cluster/${selectedCluster.value.id}/calculate`, {
            usage: {
                memory_gb: resources.value.memory / 1024, // 转换 MB 到 GB
                cpu_core: resources.value.cpu / 1000, // 转换 m 到 core
                storage_gb: resources.value.storage,
            },
            minutes: 1,
        });
        priceResult.value = response.data;
    } catch (error: any) {
        calculationError.value = error.response?.data?.message || '计算失败，请稍后重试';
        priceResult.value = null;
    } finally {
        isCalculating.value = false;
    }
};

// 防抖计算价格
let calculatePriceTimeout: number;
const debouncedCalculatePrice = () => {
    clearTimeout(calculatePriceTimeout);
    calculatePriceTimeout = setTimeout(calculatePrice, 500);
};

// 监听资源变化
watch(resources, debouncedCalculatePrice, { deep: true });

// 预设配置
const applyPreset = (type: 'light' | 'standard' | 'performance') => {
    const presets = {
        light: { memory: 512, cpu: 500, storage: 0 },
        standard: { memory: 1024, cpu: 1000, storage: 10 },
        performance: { memory: 2048, cpu: 2000, storage: 50 },
    };
    resources.value = { ...presets[type] };
};

// 格式化价格
const formatPrice = (price: number) => {
    return (price / 100).toFixed(4);
};

// 成本优化建议
const optimizationTips = computed(() => {
    const tips: string[] = [];

    if (resources.value.memory > 2048) {
        tips.push('考虑使用自动扩缩容，避免资源浪费');
    }

    if (resources.value.cpu > 2000) {
        tips.push('高CPU配置适合计算密集型应用');
    }

    if (resources.value.storage > 100) {
        tips.push('大容量存储建议选择高性能SSD');
    }

    return tips;
});

// 打字机动画相关
const apps = ['Docker', 'WordPress', 'MySQL', 'Redis', 'PostgreSQL', 'MongoDB', 'Nginx', 'Node.js', 'Python', 'Java', 'React', 'Vue.js'];
const currentApp = ref('Docker');
const appIndex = ref(0);
const charIndex = ref(0);
const isDeleting = ref(false);
const typewriterSpeed = ref(100);

// 打字机动画函数
const typewriterAnimation = () => {
    const currentText = apps[appIndex.value];

    if (isDeleting.value) {
        currentApp.value = currentText.substring(0, charIndex.value - 1);
        charIndex.value--;
        typewriterSpeed.value = 50; // 删除更快
    } else {
        currentApp.value = currentText.substring(0, charIndex.value + 1);
        charIndex.value++;
        typewriterSpeed.value = 120; // 打字稍慢，更真实
    }

    if (!isDeleting.value && charIndex.value === currentText.length) {
        // 完成打字，等待一段时间后开始删除
        setTimeout(() => {
            isDeleting.value = true;
        }, 1500); // 停留时间稍短
    } else if (isDeleting.value && charIndex.value === 0) {
        // 完成删除，切换到下一个应用
        isDeleting.value = false;
        appIndex.value = (appIndex.value + 1) % apps.length;
    }

    setTimeout(typewriterAnimation, typewriterSpeed.value);
};

// 支持的应用列表
const supportedApps = ref([
    { name: 'WordPress', description: '博客建站', icon: Globe },
    { name: 'MySQL', description: '关系型数据库', icon: Database },
    { name: 'Redis', description: '缓存数据库', icon: Server },
    { name: 'PostgreSQL', description: '高级关系型数据库', icon: Database },
    { name: 'MongoDB', description: '文档数据库', icon: Database },
    { name: 'Nginx', description: 'Web服务器', icon: Server },
    { name: 'Node.js', description: 'JavaScript后端', icon: Globe },
    { name: 'Spring Boot', description: 'Java微服务', icon: Server },
    { name: 'Django', description: 'Python Web', icon: Globe },
    { name: 'RabbitMQ', description: '消息队列', icon: Network },
    { name: 'Elasticsearch', description: '搜索引擎', icon: Database },
    { name: 'Jenkins', description: 'CI/CD', icon: Server },
]);

// 定价信息
const pricingItems = ref([
    {
        name: '内存',
        price: '¥0.001',
        unit: '每512MB/分钟',
        description: '高性能内存资源',
        icon: MemoryStick,
    },
    {
        name: 'CPU',
        price: '¥0.002',
        unit: '每500m/分钟',
        description: '弹性计算资源',
        icon: Cpu,
    },
    {
        name: '存储',
        price: '¥0.0005',
        unit: '每GB/分钟',
        description: '高速SSD存储',
        icon: HardDrive,
    },
    {
        name: '负载均衡',
        price: '¥0.01',
        unit: '每实例/分钟',
        description: '高可用保障',
        icon: LoaderCircle,
    },
]);

// 滚动相关功能
const showBackToTop = ref(false);

const scrollToFeatures = () => {
    const element = document.querySelector('#features');
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    }
};

const scrollToPricingComparison = () => {
    // 滚动到PricingComparison组件位置
    const element = document.querySelector('#pricing-comparison');
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    } else {
        // 如果没有找到特定ID，尝试滚动到PricingComparison组件的父容器
        const sections = document.querySelectorAll('section');
        for (const section of sections) {
            if (section.textContent?.includes('智能价格对比') || section.textContent?.includes('价格对比')) {
                section.scrollIntoView({ behavior: 'smooth' });
                break;
            }
        }
    }
};

const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
};

// 监听滚动事件，控制返回顶部按钮显示
const handleScroll = () => {
    showBackToTop.value = window.scrollY > 300;
};

onMounted(() => {
    initLocale();
    window.addEventListener('scroll', handleScroll);
    // 启动打字机动画
    setTimeout(() => {
        // 首先清空文本，然后开始打字
        currentApp.value = '';
        charIndex.value = 0;
        typewriterAnimation();
    }, 500);
    // 获取集群数据
    fetchClusters();
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
});
</script>

<style scoped>
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.app-card {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
}

.pricing-card {
    opacity: 0;
    animation: fadeInUp 0.8s ease-out forwards;
}

/* 返回顶部按钮的过渡动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

/* 打字机动画样式 */
.typewriter-container {
    display: inline-block;
    position: relative;
}

.typewriter-cursor {
    opacity: 1;
    animation: blink 1s infinite;
    color: currentColor;
}

@keyframes blink {
    0%,
    45% {
        opacity: 1;
    }

    46%,
    100% {
        opacity: 0;
    }
}
</style>
