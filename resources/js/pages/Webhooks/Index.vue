<template>
    <AppLayout>
        <div class="space-y-6 p-4">

            <Head title="Webhook 管理" />

            <!-- 页面标题和操作 -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                        Webhook 管理
                    </h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        管理资源变更通知的 Webhook 端点
                    </p>
                </div>
                <Button @click="router.visit(route('webhooks.create'))" class="flex items-center gap-2">
                    <Plus class="h-4 w-4" />
                    创建 Webhook
                </Button>
            </div>

            <!-- Webhook 列表 -->
            <Card>
                <CardHeader>
                    <CardTitle>Webhook 端点</CardTitle>
                    <CardDescription>
                        当资源发生变化时，系统会向这些端点发送通知
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div v-if="loading" class="flex items-center justify-center py-8">
                        <Loader2 class="h-6 w-6 animate-spin" />
                    </div>

                    <div v-else-if="webhooks.length === 0" class="text-center py-8">
                        <Webhook class="h-12 w-12 mx-auto text-gray-400 mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                            还没有 Webhook
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            创建第一个 Webhook 端点来接收资源变更通知
                        </p>
                        <Button @click="router.visit(route('webhooks.create'))">
                            创建 Webhook
                        </Button>
                    </div>

                    <div v-else class="space-y-4">
                        <div v-for="webhook in webhooks" :key="webhook.id"
                            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <h3 class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ webhook.name }}
                                        </h3>
                                        <Badge :variant="webhook.is_active ? 'default' : 'secondary'">
                                            {{ webhook.is_active ? '启用' : '禁用' }}
                                        </Badge>
                                        <Badge v-if="webhook.success_rate !== null" variant="outline">
                                            成功率 {{ webhook.success_rate }}%
                                        </Badge>
                                    </div>

                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                        {{ webhook.url }}
                                    </p>

                                    <div class="flex flex-wrap gap-2 mb-2">
                                        <Badge v-for="resourceType in webhook.resource_types" :key="resourceType"
                                            variant="outline" class="text-xs">
                                            {{ resourceTypes[resourceType] || resourceType }}
                                        </Badge>
                                    </div>

                                    <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                                        <span>{{ webhook.deliveries_count || 0 }} 次发送</span>
                                        <span>创建于 {{ formatDate(webhook.created_at) }}</span>
                                    </div>
                                </div>

                                <div class="flex items-center gap-2">
                                    <Button variant="ghost" size="sm" @click="testWebhook(webhook)"
                                        :disabled="testing === webhook.id">
                                        <Zap v-if="testing !== webhook.id" class="h-4 w-4" />
                                        <Loader2 v-else class="h-4 w-4 animate-spin" />
                                        测试
                                    </Button>

                                    <Button variant="ghost" size="sm"
                                        @click="router.visit(route('webhooks.show', webhook.id))">
                                        <Eye class="h-4 w-4" />
                                        查看
                                    </Button>

                                    <Button variant="ghost" size="sm"
                                        @click="router.visit(route('webhooks.edit', webhook.id))">
                                        <Edit class="h-4 w-4" />
                                        编辑
                                    </Button>

                                    <Button variant="ghost" size="sm" @click="deleteWebhook(webhook)"
                                        :disabled="deleting === webhook.id">
                                        <Trash2 v-if="deleting !== webhook.id" class="h-4 w-4" />
                                        <Loader2 v-else class="h-4 w-4 animate-spin" />
                                        删除
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Head, router } from '@inertiajs/vue3';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Eye, Loader2, Plus, Trash2, Webhook, Zap } from 'lucide-vue-next';
import { toast } from 'sonner';
import axios from 'axios';
import type { WebhookEndpoint } from '@/types/webhook';
import AppLayout from '@/layouts/AppLayout.vue';

interface Props {
    workspace: any;
    resourceTypes: Record<string, string>;
    eventTypes: Record<string, string>;
}

const props = defineProps<Props>();

const webhooks = ref<WebhookEndpoint[]>([]);
const loading = ref(true);
const testing = ref<number | null>(null);
const deleting = ref<number | null>(null);

const loadWebhooks = async () => {
    try {
        loading.value = true;
        const response = await axios.get('/api/webhook-endpoints');
        webhooks.value = response.data.data || response.data;
    } catch (error) {
        console.error('加载 Webhook 失败:', error);
        toast.error('加载 Webhook 失败');
    } finally {
        loading.value = false;
    }
};

const testWebhook = async (webhook: WebhookEndpoint) => {
    try {
        testing.value = webhook.id;
        const response = await axios.post(`/api/webhook-endpoints/${webhook.id}/test`);

        if (response.data.success) {
            toast.success('Webhook 测试成功');
        } else {
            toast.error(`Webhook 测试失败: ${response.data.error}`);
        }
    } catch (error: any) {
        console.error('测试 Webhook 失败:', error);
        toast.error('测试 Webhook 失败');
    } finally {
        testing.value = null;
    }
};

const deleteWebhook = async (webhook: WebhookEndpoint) => {
    if (!confirm(`确定要删除 Webhook "${webhook.name}" 吗？此操作不可撤销。`)) {
        return;
    }

    try {
        deleting.value = webhook.id;
        await axios.delete(`/api/webhook-endpoints/${webhook.id}`);

        webhooks.value = webhooks.value.filter(w => w.id !== webhook.id);
        toast.success('Webhook 已删除');
    } catch (error: any) {
        console.error('删除 Webhook 失败:', error);
        toast.error('删除 Webhook 失败');
    } finally {
        deleting.value = null;
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

onMounted(() => {
    loadWebhooks();
});
</script>
