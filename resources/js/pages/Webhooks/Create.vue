<template>
    <AppLayout>
        <div class="max-w-4xl mx-auto p-6">
            <Head title="创建 Webhook" />

            <!-- 页面标题 -->
            <div class="mb-8">
                <div class="flex items-center gap-4 mb-4">
                    <Button variant="ghost" size="sm" @click="router.visit(route('webhooks.index'))">
                        <ArrowLeft class="h-4 w-4" />
                        返回
                    </Button>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
                            创建 Webhook
                        </h1>
                        <p class="mt-2 text-gray-600 dark:text-gray-400">
                            配置新的 Webhook 端点来接收 Kubernetes 事件通知
                        </p>
                    </div>
                </div>
            </div>

            <!-- 表单 -->
            <form @submit.prevent="createWebhook" class="space-y-8">
                <!-- 基本信息 -->
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        基本信息
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <Label for="name">名称 *</Label>
                            <Input
                                id="name"
                                v-model="form.name"
                                placeholder="输入 Webhook 名称"
                                :class="{ 'border-red-500': form.errors.name }"
                            />
                            <p v-if="form.errors.name" class="text-sm text-red-600">{{ form.errors.name }}</p>
                        </div>

                        <div class="space-y-2">
                            <Label for="url">URL *</Label>
                            <Input
                                id="url"
                                v-model="form.url"
                                type="url"
                                placeholder="https://example.com/webhook"
                                :class="{ 'border-red-500': form.errors.url }"
                            />
                            <p v-if="form.errors.url" class="text-sm text-red-600">{{ form.errors.url }}</p>
                        </div>
                    </div>

                    <div class="mt-6 space-y-2">
                        <Label for="secret">签名密钥（可选）</Label>
                        <Input
                            id="secret"
                            v-model="form.secret"
                            type="password"
                            placeholder="用于验证 Webhook 的密钥"
                            :class="{ 'border-red-500': form.errors.secret }"
                        />
                        <p v-if="form.errors.secret" class="text-sm text-red-600">{{ form.errors.secret }}</p>
                        <p class="text-sm text-gray-500">留空则不进行签名验证</p>
                    </div>

                    <div class="mt-6 space-y-2">
                        <Label for="description">描述（可选）</Label>
                        <Textarea
                            id="description"
                            v-model="form.description"
                            placeholder="描述这个 Webhook 的用途"
                            :class="{ 'border-red-500': form.errors.description }"
                        />
                        <p v-if="form.errors.description" class="text-sm text-red-600">{{ form.errors.description }}</p>
                    </div>
                </div>

                <!-- 事件监听配置 -->
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        事件监听配置
                    </h2>

                    <div class="space-y-4">
                        <!-- 监听所有事件 -->
                        <div class="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                            <input
                                type="radio"
                                id="listen-all"
                                :value="true"
                                v-model="form.listen_all_events"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mt-1"
                            />
                            <div class="flex-1">
                                <Label for="listen-all" class="text-base font-medium cursor-pointer">
                                    监听所有事件
                                </Label>
                                <p class="text-sm text-gray-500 mt-1">
                                    接收所有类型的 Kubernetes 事件通知，包括 Deployment、Pod、Service 等所有资源的变更
                                </p>
                            </div>
                        </div>

                        <!-- 选择特定事件 -->
                        <div class="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                            <input
                                type="radio"
                                id="listen-specific"
                                :value="false"
                                v-model="form.listen_all_events"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mt-1"
                            />
                            <div class="flex-1">
                                <Label for="listen-specific" class="text-base font-medium cursor-pointer">
                                    选择特定事件
                                </Label>
                                <p class="text-sm text-gray-500 mt-1 mb-4">
                                    只接收选中的事件类型，可以精确控制接收的通知
                                </p>

                                <!-- 事件类型选择 -->
                                <div v-if="!form.listen_all_events" class="space-y-4 pl-4 border-l-2 border-gray-200">
                                    <div v-for="group in eventGroups" :key="group.label" class="space-y-3">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            {{ group.label }}
                                        </h4>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                            <div v-for="event in group.events" :key="event.value" class="flex items-center space-x-2">
                                                <Checkbox
                                                    :id="`event-${event.value}`"
                                                    :checked="form.event_types.includes(event.value)"
                                                    @update:checked="(checked) => toggleEventType(event.value, checked)"
                                                />
                                                <Label :for="`event-${event.value}`" class="text-sm font-normal cursor-pointer">
                                                    {{ event.label }}
                                                </Label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <p v-if="form.errors.event_types" class="mt-2 text-sm text-red-600">{{ form.errors.event_types }}</p>
                </div>

                <!-- 高级配置 -->
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        高级配置
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <Label for="timeout">超时时间（秒）</Label>
                            <Input
                                id="timeout"
                                v-model.number="form.timeout"
                                type="number"
                                min="1"
                                max="300"
                                :class="{ 'border-red-500': form.errors.timeout }"
                            />
                            <p v-if="form.errors.timeout" class="text-sm text-red-600">{{ form.errors.timeout }}</p>
                            <p class="text-sm text-gray-500">请求超时时间，建议 30 秒</p>
                        </div>

                        <div class="space-y-2">
                            <Label for="max_retries">最大重试次数</Label>
                            <Input
                                id="max_retries"
                                v-model.number="form.max_retries"
                                type="number"
                                min="0"
                                max="10"
                                :class="{ 'border-red-500': form.errors.max_retries }"
                            />
                            <p v-if="form.errors.max_retries" class="text-sm text-red-600">{{ form.errors.max_retries }}</p>
                            <p class="text-sm text-gray-500">失败时的重试次数，建议 3 次</p>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <Button type="button" variant="outline" @click="router.visit(route('webhooks.index'))">
                        取消
                    </Button>
                    <Button type="submit" :disabled="form.processing">
                        <Loader2 v-if="form.processing" class="mr-2 h-4 w-4 animate-spin" />
                        创建 Webhook
                    </Button>
                </div>
            </form>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3';
import { reactive, ref, onMounted } from 'vue';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Loader2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import axios from 'axios';
import AppLayout from '@/layouts/AppLayout.vue';

interface Props {
    workspace: any;
}

const props = defineProps<Props>();

// 表单数据（参考 OAuthClients 的模式）
const form = reactive({
    name: '',
    url: '',
    secret: '',
    description: '',
    event_types: [] as string[],
    listen_all_events: true,
    timeout: 30,
    max_retries: 3,
    errors: {} as Record<string, string>,
    processing: false,
});

// 事件类型分组
const eventGroups = ref<Array<{label: string, events: Array<{value: string, label: string}>}>>([]);

// 获取事件类型选项
const loadEventTypes = async () => {
    try {
        const response = await axios.get('/api/webhook-endpoints/options');
        const eventTypes = response.data.event_types;

        // 简单的事件分组逻辑
        const groups = [
            {
                label: 'Deployment 事件',
                events: Object.entries(eventTypes)
                    .filter(([key]) => key.startsWith('deployment.'))
                    .map(([value, label]) => ({ value, label: label as string }))
            },
            {
                label: 'Pod 事件',
                events: Object.entries(eventTypes)
                    .filter(([key]) => key.startsWith('pod.'))
                    .map(([value, label]) => ({ value, label: label as string }))
            },
            {
                label: 'Service 事件',
                events: Object.entries(eventTypes)
                    .filter(([key]) => key.startsWith('service.'))
                    .map(([value, label]) => ({ value, label: label as string }))
            },
            {
                label: '其他事件',
                events: Object.entries(eventTypes)
                    .filter(([key]) => !key.startsWith('deployment.') && !key.startsWith('pod.') && !key.startsWith('service.'))
                    .map(([value, label]) => ({ value, label: label as string }))
            }
        ].filter(group => group.events.length > 0);

        eventGroups.value = groups;
    } catch (error) {
        console.error('加载事件类型失败:', error);
        toast.error('加载事件类型失败');
    }
};

// 切换事件类型选择
const toggleEventType = (eventType: string, checked: boolean) => {
    if (checked) {
        if (!form.event_types.includes(eventType)) {
            form.event_types.push(eventType);
        }
    } else {
        const index = form.event_types.indexOf(eventType);
        if (index > -1) {
            form.event_types.splice(index, 1);
        }
    }
};

// 创建 Webhook
const createWebhook = async () => {
    try {
        form.processing = true;
        form.errors = {};

        // 准备提交数据
        const submitData = {
            name: form.name,
            url: form.url,
            secret: form.secret || null,
            description: form.description || null,
            event_types: form.listen_all_events ? ['all'] : form.event_types,
            timeout: form.timeout,
            max_retries: form.max_retries,
        };

        await axios.post('/api/webhook-endpoints', submitData);

        toast.success('Webhook 创建成功');
        router.visit(route('webhooks.index'));
    } catch (error: any) {
        console.error('创建 Webhook 失败:', error);

        if (error.response?.status === 422) {
            form.errors = error.response.data.errors || {};
        } else {
            toast.error('创建 Webhook 失败');
        }
    } finally {
        form.processing = false;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    loadEventTypes();
});
</script>