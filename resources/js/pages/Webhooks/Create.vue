<template>
    <AppLayout>
        <div class="space-y-6 p-4">

            <Head title="创建 Webhook" />

            <!-- 页面标题 -->
            <div class="flex items-center gap-4">
                <Button variant="ghost" size="sm" @click="router.visit(route('webhooks.index'))">
                    <ArrowLeft class="h-4 w-4" />
                    返回
                </Button>
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                        创建 Webhook
                    </h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        配置新的 Webhook 端点来接收资源变更通知
                    </p>
                </div>
            </div>

            <!-- 创建表单 -->
            <Card>
                <CardHeader>
                    <CardTitle>Webhook 配置</CardTitle>
                    <CardDescription>
                        填写 Webhook 端点的基本信息和监听配置
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="createWebhook" class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <Label for="name">名称 *</Label>
                                <Input id="name" v-model="form.name" placeholder="输入 Webhook 名称"
                                    :class="{ 'border-red-500': errors.name }" />
                                <p v-if="errors.name" class="text-sm text-red-600">{{ errors.name }}</p>
                            </div>

                            <div class="space-y-2">
                                <Label for="url">URL *</Label>
                                <Input id="url" v-model="form.url" type="url" placeholder="https://example.com/webhook"
                                    :class="{ 'border-red-500': errors.url }" />
                                <p v-if="errors.url" class="text-sm text-red-600">{{ errors.url }}</p>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <Label for="secret">签名密钥（可选）</Label>
                            <Input id="secret" v-model="form.secret" placeholder="用于验证 Webhook 请求的密钥"
                                :class="{ 'border-red-500': errors.secret }" />
                            <p v-if="errors.secret" class="text-sm text-red-600">{{ errors.secret }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                如果设置了密钥，系统会在请求头中添加签名用于验证
                            </p>
                        </div>

                        <div class="space-y-2">
                            <Label for="description">描述（可选）</Label>
                            <Textarea id="description" v-model="form.description" placeholder="描述这个 Webhook 的用途"
                                :class="{ 'border-red-500': errors.description }" />
                            <p v-if="errors.description" class="text-sm text-red-600">{{ errors.description }}</p>
                        </div>

                        <!-- 监听配置 -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                监听配置
                            </h3>

                            <div class="space-y-2">
                                <Label>资源类型 *</Label>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    <div v-for="(label, value) in resourceTypes" :key="value"
                                        class="flex items-center space-x-2">
                                        <Checkbox :id="`resource-${value}`"
                                            :checked="form.resource_types.includes(value)"
                                            @update:checked="toggleResourceType(value)" />
                                        <Label :for="`resource-${value}`" class="text-sm">{{ label }}</Label>
                                    </div>
                                </div>
                                <p v-if="errors.resource_types" class="text-sm text-red-600">{{ errors.resource_types }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label>事件类型 *</Label>
                                <div class="flex gap-6">
                                    <div v-for="(label, value) in eventTypes" :key="value"
                                        class="flex items-center space-x-2">
                                        <Checkbox :id="`event-${value}`" :checked="form.event_types.includes(value)"
                                            @update:checked="toggleEventType(value)" />
                                        <Label :for="`event-${value}`" class="text-sm">{{ label }}</Label>
                                    </div>
                                </div>
                                <p v-if="errors.event_types" class="text-sm text-red-600">{{ errors.event_types }}</p>
                            </div>
                        </div>

                        <!-- 高级配置 -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                高级配置
                            </h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <Label for="timeout">超时时间（秒）</Label>
                                    <Input id="timeout" v-model.number="form.timeout" type="number" min="5" max="300"
                                        :class="{ 'border-red-500': errors.timeout }" />
                                    <p v-if="errors.timeout" class="text-sm text-red-600">{{ errors.timeout }}</p>
                                </div>

                                <div class="space-y-2">
                                    <Label for="max_retries">最大重试次数</Label>
                                    <Input id="max_retries" v-model.number="form.max_retries" type="number" min="0"
                                        max="10" :class="{ 'border-red-500': errors.max_retries }" />
                                    <p v-if="errors.max_retries" class="text-sm text-red-600">{{ errors.max_retries }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="flex items-center gap-4 pt-6">
                            <Button type="submit" :disabled="creating">
                                <Loader2 v-if="creating" class="h-4 w-4 animate-spin mr-2" />
                                创建 Webhook
                            </Button>
                            <Button type="button" variant="outline" @click="router.visit(route('webhooks.index'))">
                                取消
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Head, router } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Loader2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import axios from 'axios';
import type { WebhookFormData } from '@/types/webhook';
import AppLayout from '@/layouts/AppLayout.vue';

interface Props {
    workspace: any;
    resourceTypes: Record<string, string>;
    eventTypes: Record<string, string>;
}

const props = defineProps<Props>();

const form = reactive<WebhookFormData>({
    name: '',
    url: '',
    secret: '',
    resource_types: [],
    event_types: ['created', 'updated', 'deleted'],
    description: '',
    timeout: 30,
    max_retries: 3,
});

const errors = ref<Record<string, string>>({});
const creating = ref(false);

const toggleResourceType = (resourceType: string) => {
    const index = form.resource_types.indexOf(resourceType);
    if (index > -1) {
        form.resource_types.splice(index, 1);
    } else {
        form.resource_types.push(resourceType);
    }
};

const toggleEventType = (eventType: string) => {
    const index = form.event_types.indexOf(eventType);
    if (index > -1) {
        form.event_types.splice(index, 1);
    } else {
        form.event_types.push(eventType);
    }
};

const createWebhook = async () => {
    try {
        creating.value = true;
        errors.value = {};

        await axios.post('/api/webhook-endpoints', form);

        toast.success('Webhook 创建成功');
        router.visit(route('webhooks.index'));
    } catch (error: any) {
        console.error('创建 Webhook 失败:', error);

        if (error.response?.status === 422) {
            errors.value = error.response.data.errors || {};
        } else {
            toast.error('创建 Webhook 失败');
        }
    } finally {
        creating.value = false;
    }
};
</script>
