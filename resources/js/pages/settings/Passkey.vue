<script setup lang="ts">
import { Head, router, useForm } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

import HeadingSmall from '@/components/HeadingSmall.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { type BreadcrumbItem } from '@/types';
import { AlertCircle, Key, Plus, Smartphone, Trash2 } from 'lucide-vue-next';

interface Passkey {
    id: string;
    name: string;
    last_used_at: string | null;
}

interface Props {
    passkeys: Passkey[];
}

const props = defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: '通行密钥设置',
        href: '/settings/passkey',
    },
];

const isAdding = ref(false);
const newPasskeyName = ref('');
const errorMessage = ref('');

const form = useForm({
    name: '',
});

const hasPasskeys = computed(() => props.passkeys.length > 0);

const formatDate = (dateString: string | null) => {
    if (!dateString) return '从未使用';
    return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const addPasskey = async () => {
    errorMessage.value = '';

    if (!window.browserSupportsWebAuthn || !window.browserSupportsWebAuthn()) {
        errorMessage.value = '您的浏览器不支持通行密钥功能';
        return;
    }

    if (!newPasskeyName.value.trim()) {
        errorMessage.value = '请输入通行密钥名称';
        return;
    }

    try {
        isAdding.value = true;

        // 获取注册选项
        const response = await fetch(window.route('passkey.generate-options'));
        if (!response.ok) {
            throw new Error('获取注册选项失败');
        }

        const options = await response.json();

        // 开始注册流程
        const credential = await window.startRegistration(options);

        // 提交注册数据
        router.post(
            window.route('passkey.store'),
            {
                options: JSON.stringify(options),
                passkey: JSON.stringify(credential),
                name: newPasskeyName.value.trim(),
            },
            {
                onSuccess: () => {
                    newPasskeyName.value = '';
                    errorMessage.value = '';
                },
                onError: (errors) => {
                    errorMessage.value = errors.passkey || '创建通行密钥时出现错误';
                },
            },
        );
    } catch (error) {
        console.error('创建通行密钥失败:', error);
        errorMessage.value = '创建通行密钥失败，请重试';
    } finally {
        isAdding.value = false;
    }
};

const deletePasskey = (passkey: Passkey) => {
    if (!confirm(`确定要删除通行密钥 "${passkey.name}" 吗？`)) {
        return;
    }

    router.delete(window.route('passkey.destroy', { id: passkey.id }));
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="通行密钥设置" />

        <SettingsLayout>
            <div class="space-y-6">
                <HeadingSmall title="通行密钥" description="使用通行密钥安全地登录您的账户，无需输入密码" />

                <!-- 添加新通行密钥表单 -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <Plus class="h-5 w-5" />
                            添加新通行密钥
                        </CardTitle>
                        <CardDescription>
                            为您的账户创建一个新的通行密钥。通行密钥使用您的设备（如指纹、面部识别或安全密钥）来验证身份。
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-4">
                            <div class="space-y-2">
                                <Label for="passkey-name">通行密钥名称</Label>
                                <Input id="passkey-name" v-model="newPasskeyName" placeholder="例如：我的 iPhone、MacBook、Windows Hello 等" class="max-w-sm" />
                                <p class="text-sm text-muted-foreground">为您的通行密钥起一个便于识别的名称</p>
                            </div>

                            <!-- 错误消息 -->
                            <div v-if="errorMessage" class="flex items-center gap-2 rounded-md border border-destructive/20 bg-destructive/10 p-3">
                                <AlertCircle class="h-4 w-4 text-destructive" />
                                <p class="text-sm text-destructive">{{ errorMessage }}</p>
                            </div>

                            <Button @click="addPasskey" :disabled="isAdding || !newPasskeyName.trim()" class="w-full sm:w-auto">
                                <Key class="mr-2 h-4 w-4" />
                                {{ isAdding ? '正在创建...' : '创建通行密钥' }}
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                <!-- 已有通行密钥列表 -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <Smartphone class="h-5 w-5" />
                            我的通行密钥
                        </CardTitle>
                        <CardDescription> 管理您已创建的通行密钥 </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div v-if="hasPasskeys" class="space-y-4">
                            <div
                                v-for="(passkey, index) in passkeys"
                                :key="passkey.id"
                                class="flex items-center justify-between rounded-lg border p-4"
                            >
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                            <Key class="h-5 w-5 text-primary" />
                                        </div>
                                    </div>
                                    <div>
                                        <p class="font-medium">{{ passkey.name }}</p>
                                        <p class="text-sm text-muted-foreground">最后使用：{{ formatDate(passkey.last_used_at) }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <Badge variant="secondary">
                                        {{ passkey.last_used_at ? '已使用' : '未使用' }}
                                    </Badge>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        @click="deletePasskey(passkey)"
                                        class="text-destructive hover:bg-destructive/10 hover:text-destructive"
                                    >
                                        <Trash2 class="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>

                        <div v-else class="py-8 text-center">
                            <Key class="mx-auto h-12 w-12 text-muted-foreground/50" />
                            <h3 class="mt-2 text-sm font-medium">还没有通行密钥</h3>
                            <p class="mt-1 text-sm text-muted-foreground">创建您的第一个通行密钥来开始使用更安全的登录方式</p>
                        </div>
                    </CardContent>
                </Card>

                <!-- 帮助信息 -->
                <Card>
                    <CardHeader>
                        <CardTitle>关于通行密钥</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                        <div class="space-y-2 text-sm text-muted-foreground">
                            <p>
                                <strong>什么是通行密钥？</strong>
                                通行密钥是一种更安全、更便捷的登录方式，使用您设备的生物识别功能（如指纹、面部识别）或安全密钥来验证身份。
                            </p>
                            <p>
                                <strong>优势：</strong>
                                • 无需记忆密码 • 防止钓鱼攻击 • 更快的登录速度 • 更高的安全性
                            </p>
                            <p>
                                <strong>支持的设备：</strong>
                                iPhone、iPad、Mac、Android 设备、Windows Hello、硬件安全密钥(比如 YubiKey)等
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
