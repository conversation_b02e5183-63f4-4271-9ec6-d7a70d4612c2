<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3';
import { ref } from 'vue';

import HeadingSmall from '@/components/HeadingSmall.vue';
import OTPLogin from '@/components/OTPLogin.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { type BreadcrumbItem } from '@/types';
import { Key, Plus, Shield, Trash2 } from 'lucide-vue-next';

interface OTPDevice {
    id: string;
    device_id: string;
    created_at: string;
}

interface Props {
    devices: OTPDevice[];
}

const props = defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'YubiKey 设置',
        href: '/settings/otp',
    },
];

const showAddDevice = ref(false);

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const deleteDevice = (device: OTPDevice) => {
    if (!confirm(`确定要删除 YubiKey 设备 "${device.device_id}" 吗？`)) {
        return;
    }

    router.delete(window.route('otp.destroy', { device_id: device.device_id }));
};

const handleAddDeviceSuccess = () => {
    showAddDevice.value = false;
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="YubiKey 设置" />

        <SettingsLayout>
            <div class="space-y-6">
                <HeadingSmall title="YubiKey" description="使用 YubiKey 设备进行双因素认证，提供更高的账户安全性" />

                <!-- 添加新设备按钮 -->
                <div class="flex justify-end">
                    <Button @click="showAddDevice = !showAddDevice">
                        <Plus class="mr-2 h-4 w-4" />
                        {{ showAddDevice ? '取消添加' : '添加设备' }}
                    </Button>
                </div>

                <!-- 添加设备表单 -->
                <Card v-if="showAddDevice">
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <Plus class="h-5 w-5" />
                            添加新的 YubiKey 设备
                        </CardTitle>
                        <CardDescription> 请插入您的 YubiKey 设备，然后触摸按钮来注册设备 </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div class="mx-auto max-w-md">
                            <OTPLogin
                                title="添加 YubiKey 设备"
                                description="请插入并触摸 YubiKey"
                                :submit-route="route('otp.store')"
                                footer-text="触摸设备按钮来注册"
                            />
                        </div>
                    </CardContent>
                </Card>

                <!-- 已有设备列表 -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <Shield class="h-5 w-5" />
                            我的 YubiKey 设备
                        </CardTitle>
                        <CardDescription> 管理您已注册的 YubiKey 设备 </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div v-if="devices.length > 0" class="space-y-4">
                            <div v-for="device in devices" :key="device.id" class="flex items-center justify-between rounded-lg border p-4">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                            <Key class="h-5 w-5 text-primary" />
                                        </div>
                                    </div>
                                    <div>
                                        <p class="font-medium">YubiKey 设备</p>
                                        <p class="text-sm text-muted-foreground">设备 ID: {{ device.device_id }}</p>
                                        <p class="text-sm text-muted-foreground">添加时间：{{ formatDate(device.created_at) }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <Badge variant="secondary"> 已激活 </Badge>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        @click="deleteDevice(device)"
                                        class="text-destructive hover:bg-destructive/10 hover:text-destructive"
                                    >
                                        <Trash2 class="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>

                        <div v-else class="py-8 text-center">
                            <Shield class="mx-auto h-12 w-12 text-muted-foreground/50" />
                            <h3 class="mt-2 text-sm font-medium">还没有 YubiKey 设备</h3>
                            <p class="mt-1 text-sm text-muted-foreground">添加您的第一个 YubiKey 设备来增强账户安全</p>
                        </div>
                    </CardContent>
                </Card>

                <!-- 帮助信息 -->
                <Card>
                    <CardHeader>
                        <CardTitle>关于 YubiKey OTP</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                        <div class="space-y-2 text-sm text-muted-foreground">
                            <p>
                                <strong>什么是 YubiKey OTP？</strong>
                                YubiKey OTP 是一种硬件安全密钥，通过生成一次性密码来提供双因素认证。
                            </p>
                            <p>
                                <strong>如何使用：</strong>
                                • 插入 YubiKey 设备到 USB 端口<br />
                                • 触摸设备上的按钮生成 OTP<br />
                                • 系统会自动验证并登录
                            </p>
                            <p>
                                <strong>安全特性：</strong>
                                • 硬件级别的安全性<br />
                                • 防止钓鱼攻击<br />
                                • 无需安装额外软件<br />
                                • 支持多种操作系统
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
