<template>
    <AppLayout>
        <Head title="OAuth 客户端" />

        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">OAuth 客户端</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">管理你的 OAuth 2.0 客户端应用程序</p>
                </div>
                <Button @click="openCreateDialog">
                    <Plus class="mr-2 h-4 w-4" />
                    创建客户端
                </Button>
            </div>

            <!-- 客户端密钥显示 -->
            <div v-if="newClientSecret" class="rounded-md border border-blue-200 bg-blue-50 p-4 dark:border-blue-500/30 dark:bg-blue-950/20">
                <div class="flex">
                    <Info class="h-5 w-5 text-blue-400" />
                    <div class="ml-3">
                        <p class="mb-2 text-sm font-medium text-blue-800 dark:text-blue-300">客户端密钥已生成，请立即复制保存：</p>
                        <div class="relative">
                            <pre class="rounded border bg-blue-100 p-3 font-mono text-sm break-all dark:bg-blue-900/50">{{ newClientSecret }}</pre>
                            <Button size="sm" variant="outline" class="absolute top-2 right-2" @click="copySecret">
                                <Copy class="h-3 w-3" />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 客户端列表 -->
            <div class="rounded-lg border">
                <div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">我的客户端</h3>
                </div>

                <div v-if="clientsList.length === 0" class="p-8 text-center">
                    <div class="text-gray-400 dark:text-gray-500">
                        <Key class="mx-auto mb-4 h-12 w-12" />
                        <p class="text-sm">暂无 OAuth 客户端</p>
                        <p class="mt-1 text-xs">点击上方按钮创建你的第一个客户端</p>
                    </div>
                </div>

                <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
                    <div v-for="client in clientsList" :key="client.id" class="p-6 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50">
                        <div class="flex items-start justify-between">
                            <div class="min-w-0 flex-1">
                                <div class="mb-2 flex items-center gap-3">
                                    <!-- 客户端图标 -->
                                    <div class="flex-shrink-0">
                                        <img
                                            v-if="client.icon_url"
                                            :src="client.icon_url"
                                            :alt="client.name"
                                            class="h-10 w-10 rounded-lg object-cover"
                                        />
                                        <div v-else class="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800">
                                            <Key class="h-5 w-5 text-gray-400" />
                                        </div>
                                    </div>

                                    <div class="min-w-0 flex-1">
                                        <h4 class="truncate text-sm font-medium text-gray-900 dark:text-white">
                                            {{ client.name }}
                                        </h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">客户端 ID：{{ client.id }}</p>
                                        <div class="mt-1 flex flex-wrap gap-1">
                                            <Badge v-if="client.trusted" variant="secondary">受信任</Badge>
                                            <Badge v-if="client.is_pkce_client" variant="outline">PKCE</Badge>
                                            <Badge v-if="client.supports_device_flow" variant="outline">Device Flow </Badge>
                                        </div>
                                    </div>
                                </div>
                                <p class="mb-1 truncate text-sm text-gray-500 dark:text-gray-400">
                                    <span class="font-medium">回调地址：</span>{{ client.redirect }}
                                </p>
                                <p class="text-xs text-gray-400 dark:text-gray-500">创建时间：{{ client.created_at }}</p>
                            </div>

                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                        <MoreHorizontal class="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem @click="openEditDialog(client)">
                                        <Edit class="mr-2 h-4 w-4" />
                                        编辑
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem @click="confirmDeleteClient(client)" class="text-red-600 dark:text-red-400">
                                        <Trash2 class="mr-2 h-4 w-4" />
                                        删除
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </div>
            </div>

            <!-- OAuth/OpenID 端点信息卡片 -->
            <div class="mx-auto mt-8 max-w-2xl">
                <Card>
                    <CardHeader>
                        <CardTitle>OAuth / OpenID 端点信息</CardTitle>
                        <CardDescription>常用端点及其请求方式</CardDescription>
                    </CardHeader>
                    <CardContent class="p-0">
                        <div class="divide-y divide-gray-200 dark:divide-gray-700">
                            <div class="flex flex-col px-4 py-3 sm:flex-row sm:items-center sm:justify-between">
                                <div>
                                    <span class="font-medium">OpenID Connect 发现</span>
                                    <span class="ml-2 text-xs text-gray-500">GET</span>
                                </div>
                                <code class="mt-2 rounded bg-muted px-2 py-1 text-xs break-all sm:mt-0">{{ route('public.openid.discovery') }}</code>
                            </div>
                            <div class="flex flex-col px-4 py-3 sm:flex-row sm:items-center sm:justify-between">
                                <div>
                                    <span class="font-medium">JWKs (JSON Web Key Sets) 端点</span>
                                    <span class="ml-2 text-xs text-gray-500">GET</span>
                                </div>
                                <code class="mt-2 rounded bg-muted px-2 py-1 text-xs break-all sm:mt-0">{{ route('public.openid.jwks') }}</code>
                            </div>
                            <div class="flex flex-col px-4 py-3 sm:flex-row sm:items-center sm:justify-between">
                                <div>
                                    <span class="font-medium">授权端点</span>
                                    <span class="ml-2 text-xs text-gray-500">GET</span>
                                </div>
                                <code class="mt-2 rounded bg-muted px-2 py-1 text-xs break-all sm:mt-0">{{
                                    route('passport.authorizations.authorize')
                                }}</code>
                            </div>
                            <div class="flex flex-col px-4 py-3 sm:flex-row sm:items-center sm:justify-between">
                                <div>
                                    <span class="font-medium">令牌端点</span>
                                    <span class="ml-2 text-xs text-gray-500">POST</span>
                                </div>
                                <code class="mt-2 rounded bg-muted px-2 py-1 text-xs break-all sm:mt-0">{{ route('passport.token') }}</code>
                            </div>
                            <div class="flex flex-col px-4 py-3 sm:flex-row sm:items-center sm:justify-between">
                                <div>
                                    <span class="font-medium">用户信息端点</span>
                                    <span class="ml-2 text-xs text-gray-500">GET</span>
                                </div>
                                <code class="mt-2 rounded bg-muted px-2 py-1 text-xs break-all sm:mt-0">{{ route('api.user.index') }}</code>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- 创建/编辑客户端对话框 -->
            <Dialog v-model:open="showClientDialog">
                <DialogContent class="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>{{ editingClient ? '编辑客户端' : '创建客户端' }}</DialogTitle>
                    </DialogHeader>

                    <form @submit.prevent="saveClient">
                        <div class="space-y-4">
                            <!-- 图标上传 -->
                            <div>
                                <Label>客户端图标</Label>
                                <div class="mt-2 flex items-center gap-4">
                                    <!-- 图标预览 -->
                                    <div class="flex-shrink-0">
                                        <img
                                            v-if="iconPreview"
                                            :src="iconPreview"
                                            alt="图标预览"
                                            class="h-16 w-16 rounded-lg border-2 border-gray-200 object-cover dark:border-gray-700"
                                        />
                                        <img
                                            v-else-if="editingClient?.icon_url"
                                            :src="editingClient.icon_url"
                                            :alt="editingClient.name"
                                            class="h-16 w-16 rounded-lg border-2 border-gray-200 object-cover dark:border-gray-700"
                                        />
                                        <div
                                            v-else
                                            class="flex h-16 w-16 items-center justify-center rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600"
                                        >
                                            <Key class="h-6 w-6 text-gray-400" />
                                        </div>
                                    </div>

                                    <!-- 上传按钮 -->
                                    <div class="flex-1">
                                        <input ref="iconInput" type="file" accept="image/*" class="hidden" @change="handleIconChange" />
                                        <Button type="button" variant="outline" @click="iconInput?.click()">
                                            <Upload class="mr-2 h-4 w-4" />
                                            选择图标
                                        </Button>
                                        <p class="mt-1 text-xs text-gray-500">支持 PNG、JPG、GIF、SVG，最大 2MB</p>
                                        <p v-if="clientForm.errors.icon" class="mt-1 text-xs text-red-600">
                                            {{ clientForm.errors.icon }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <Label for="client-name">客户端名称</Label>
                                <Input
                                    id="client-name"
                                    v-model="clientForm.name"
                                    placeholder="我的应用程序"
                                    :class="{ 'border-red-500': clientForm.errors.name }"
                                />
                                <p v-if="clientForm.errors.name" class="mt-1 text-sm text-red-600">
                                    {{ clientForm.errors.name }}
                                </p>
                            </div>

                            <div>
                                <Label for="client-redirect">回调地址</Label>
                                <Input
                                    id="client-redirect"
                                    v-model="clientForm.redirect"
                                    placeholder="https://example.com/auth/callback"
                                    :class="{ 'border-red-500': clientForm.errors.redirect }"
                                />
                                <p v-if="clientForm.errors.redirect" class="mt-1 text-sm text-red-600">
                                    {{ clientForm.errors.redirect }}
                                </p>
                                <p class="mt-1 text-xs text-gray-500">用户授权后的重定向地址</p>
                            </div>

                            <!-- 客户端类型选项 - 仅在创建时显示 -->
                            <div v-if="!editingClient" class="space-y-3">
                                <div class="border-t pt-4">
                                    <Label class="text-sm font-medium">客户端类型</Label>

                                    <div class="mt-3 space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <Checkbox
                                                id="pkce-client"
                                                :model-value="clientForm.pkce_client"
                                                @update:model-value="(value) => (clientForm.pkce_client = Boolean(value))"
                                            />
                                            <div class="flex-1">
                                                <Label for="pkce-client" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    PKCE 客户端
                                                </Label>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                                    公共客户端，使用 PKCE 进行授权码流，无需客户端密钥
                                                </p>
                                            </div>
                                        </div>

                                        <div class="flex items-center space-x-3">
                                            <Checkbox
                                                id="device-flow"
                                                :model-value="clientForm.device_flow"
                                                @update:model-value="(value) => (clientForm.device_flow = Boolean(value))"
                                            />
                                            <div class="flex-1">
                                                <Label for="device-flow" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    支持设备授权流程
                                                </Label>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                                    支持设备授权流(Device Flow)，适用于命令行等无法唤起浏览器的应用程序
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <DialogFooter class="mt-6">
                            <Button type="button" variant="outline" @click="resetClientDialog" :disabled="clientForm.processing"> 取消 </Button>
                            <Button type="submit" :disabled="clientForm.processing">
                                {{ clientForm.processing ? '保存中...' : '保存' }}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent class="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>确认删除</DialogTitle>
                        <DialogDescription>
                            确定要删除客户端 "{{ clientToDelete?.name }}" 吗？ 此操作将撤销所有相关的访问令牌，且不可恢复。
                        </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false" :disabled="deleteForm.processing"> 取消 </Button>
                        <Button variant="destructive" @click="deleteClient" :disabled="deleteForm.processing">
                            {{ deleteForm.processing ? '删除中...' : '删除' }}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import type { PassportClient } from '@/types/passport';
import { Head } from '@inertiajs/vue3';
import axios from 'axios';
import { Copy, Edit, Info, Key, MoreHorizontal, Plus, Trash2, Upload } from 'lucide-vue-next';
import { toast } from 'sonner';
import { reactive, ref } from 'vue';

interface Props {
    clients: { data: PassportClient[] };
}

const props = defineProps<Props>();

// 响应式数据
const clientsList = ref([...props.clients.data]);
const newClientSecret = ref<string>('');
const iconPreview = ref<string>('');
const selectedIcon = ref<File | null>(null);
const iconInput = ref<HTMLInputElement>();

// 表单状态
const showClientDialog = ref(false);
const showDeleteDialog = ref(false);
const editingClient = ref<PassportClient | null>(null);
const clientToDelete = ref<PassportClient | null>(null);

// 表单数据
const clientForm = reactive({
    name: '',
    redirect: '',
    pkce_client: false,
    device_flow: false,
    errors: {} as Record<string, string>,
    processing: false,
});

const deleteForm = reactive({
    processing: false,
});

// 处理图标选择
const handleIconChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];

    if (file) {
        selectedIcon.value = file;

        // 创建预览
        const reader = new FileReader();
        reader.onload = (e) => {
            iconPreview.value = e.target?.result as string;
        };
        reader.readAsDataURL(file);
    }
};

// 重置客户端表单
const resetClientForm = () => {
    clientForm.name = '';
    clientForm.redirect = '';
    clientForm.pkce_client = false;
    clientForm.device_flow = false;
    clientForm.errors = {};
    clientForm.processing = false;
    selectedIcon.value = null;
    iconPreview.value = '';
};

// 打开创建对话框
const openCreateDialog = () => {
    editingClient.value = null;
    resetClientForm();
    clientForm.redirect = window.location.origin + '/auth/callback';
    showClientDialog.value = true;
};

// 打开编辑对话框
const openEditDialog = (client: PassportClient) => {
    editingClient.value = client;
    clientForm.name = client.name;
    clientForm.redirect = client.redirect;
    clientForm.errors = {};
    selectedIcon.value = null;
    iconPreview.value = '';
    showClientDialog.value = true;
};

// 重置对话框
const resetClientDialog = () => {
    showClientDialog.value = false;
    resetClientForm();
};

// 保存客户端
const saveClient = async () => {
    clientForm.processing = true;
    clientForm.errors = {};

    try {
        const formData = new FormData();
        formData.append('name', clientForm.name);
        formData.append('redirect', clientForm.redirect);

        if (selectedIcon.value) {
            formData.append('icon', selectedIcon.value);
        }

        // 仅在创建时包含客户端类型选项
        if (!editingClient.value) {
            formData.append('pkce_client', clientForm.pkce_client ? '1' : '0');
            formData.append('device_flow', clientForm.device_flow ? '1' : '0');
        }

        let response;
        if (editingClient.value) {
            // 更新
            formData.append('_method', 'PUT');
            response = await axios.post(route('oauth.clients.update', editingClient.value.id), formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            // 更新列表中的客户端
            const index = clientsList.value.findIndex((c) => c.id === editingClient.value!.id);
            if (index !== -1) {
                clientsList.value[index] = response.data.client;
            }
        } else {
            // 创建
            response = await axios.post(route('oauth.clients.store'), formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            // 添加到列表
            clientsList.value.unshift(response.data.client);
            newClientSecret.value = response.data.secret;
        }

        toast.success(response.data.message);
        resetClientDialog();
    } catch (error: any) {
        if (error.response?.status === 422) {
            clientForm.errors = error.response.data.errors || {};
        } else {
            toast.error('操作失败，请重试');
        }
    } finally {
        clientForm.processing = false;
    }
};

// 确认删除客户端
const confirmDeleteClient = (client: PassportClient) => {
    clientToDelete.value = client;
    showDeleteDialog.value = true;
};

// 删除客户端
const deleteClient = async () => {
    if (!clientToDelete.value) return;

    deleteForm.processing = true;

    try {
        const response = await axios.delete(route('oauth.clients.destroy', clientToDelete.value.id));

        // 从列表中移除
        const index = clientsList.value.findIndex((c) => c.id === clientToDelete.value!.id);
        if (index !== -1) {
            clientsList.value.splice(index, 1);
        }

        showDeleteDialog.value = false;
        toast.success(response.data.message);
    } catch (error) {
        toast.error('删除失败，请重试');
    } finally {
        deleteForm.processing = false;
    }
};

// 复制密钥
const copySecret = async () => {
    if (newClientSecret.value) {
        try {
            await navigator.clipboard.writeText(newClientSecret.value);
            toast.success('密钥已复制到剪贴板');
            newClientSecret.value = ''; // 清除显示
        } catch (err) {
            toast.error('复制失败，请手动复制');
        }
    }
};
</script>
