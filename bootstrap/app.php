<?php

use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\JSONRequest;
use App\Http\Middleware\WorkspacePermission;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Laravel\Passport\Http\Middleware\CreateFreshApiToken;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
        then: function () {
            /* 公开路由，用于处理不需要认证的请求 */
            Route::group([
                'as' => 'public.',
                'middleware' => 'web',
            ], base_path('routes/public.php'));

            /* 其他路由 */
            Route::middleware('web')->group(function () {
                require __DIR__.'/../routes/auth.php';
                require __DIR__.'/../routes/settings.php';
            });

            /* Passport 路由，用于处理 Passport 相关的请求 */
            Route::group([
                'as' => 'passport.',
                'prefix' => 'oauth',
                'namespace' => '\Laravel\Passport\Http\Controllers',
            ], function () {
                require __DIR__.'/../routes/passport.php';
            });

            /* 管理员路由 */
            Route::prefix('admin')->as('admin.')
                ->middleware(['web', 'auth:admin'])
                ->group(base_path('routes/admin.php'));

        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state']);

        $middleware->api(append: [
            JSONRequest::class,
            WorkspacePermission::class,
        ]);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
            WorkspacePermission::class,
            CreateFreshApiToken::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->respond(function (\Symfony\Component\HttpFoundation\Response $response, Throwable $exception, Request $request) {
            if (! app()->environment(['local', 'testing']) && in_array($response->getStatusCode(), [500, 503, 404, 403])) {
                return Inertia::render('ErrorPage', ['status' => $response->getStatusCode()])
                    ->toResponse($request)
                    ->setStatusCode($response->getStatusCode());
            } elseif ($response->getStatusCode() === 419) {
                return back()->with([
                    'message' => 'The page expired, please try again.',
                ]);
            }

            return $response;
        });
    })->create();
