<?php

use App\Http\Controllers\Admin\AuthController;
use Illuminate\Support\Facades\Route;

Route::get('/', [AuthController::class, 'showLoginForm'])->name('login');

Route::withoutMiddleware(['auth:web', 'auth:admin'])->group(function () {
    Route::get('/', [AuthController::class, 'showLoginForm'])->name('login');
    Route::middleware(['throttle:10,1'])->post('/', [AuthController::class, 'login']);
});

Route::get('/home', [AuthController::class, 'index'])->name('index');

Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
