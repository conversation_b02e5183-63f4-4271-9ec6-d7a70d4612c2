<?php

use App\Http\Controllers\Api\ChatCompletionController;
use App\Http\Controllers\Api\ClusterController;
use App\Http\Controllers\Api\ClusterPricingHistoryController;
use App\Http\Controllers\Api\ConfigMapController;
use App\Http\Controllers\Api\DeploymentController;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\HorizontalPodAutoscalerController;
use App\Http\Controllers\Api\IngressController;
use App\Http\Controllers\Api\IpPoolController;
use App\Http\Controllers\Api\MetricsController;
use App\Http\Controllers\Api\PaymentMethodController;
use App\Http\Controllers\Api\PodController;
use App\Http\Controllers\Api\PodTerminalController;
use App\Http\Controllers\Api\PricingController;
use App\Http\Controllers\Api\SecretController;
use App\Http\Controllers\Api\ServiceController;
use App\Http\Controllers\Api\StatefulSetController;
use App\Http\Controllers\Api\StorageController;
use App\Http\Controllers\Api\TaskController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\WebhookEndpointController;
use App\Http\Controllers\Api\WorkspaceController;
use App\Http\Controllers\ConfigurationController;
use App\Http\Middleware\MustInWorkspace;
use App\Http\Middleware\WorkspaceMustActive;
use Illuminate\Support\Facades\Route;

// 公开的 API 路由（无需认证）
Route::middleware(['throttle:60,1'])->name('api.')->group(function () {
    // 集群相关的公开 API（用于价格计算器）
    Route::apiResource('clusters', ClusterController::class)->only(['index']);
    Route::get('/clusters/{cluster}/pricing-history', [ClusterPricingHistoryController::class, 'index']);
    Route::get('/clusters/pricing-latest', [ClusterPricingHistoryController::class, 'latest']);

    // 通用价格查询 API
    Route::get('/pricing/unit-types', [PricingController::class, 'getUnitTypes']);
    Route::get('/pricing/resource-types', [PricingController::class, 'getSupportedResourceTypes']);
    Route::get('/pricing/{resourceType}/{resourceId}', [PricingController::class, 'getResourcePricing']);
    Route::post('/pricing/{resourceType}/{resourceId}/calculate', [PricingController::class, 'calculateCost']);

    // 配置相关的公开 API
    Route::get('/config/labels', [ConfigurationController::class, 'getLabelsConfig']);
});

// 需要认证的 API 路由
Route::middleware(['auth:api', 'throttle:120,1'])->name('api.')->group(function () {
    Route::get('/user', [UserController::class, 'index'])->name('user.index');

    // ChatCompletion API 路由（OpenAI 反向代理）
    Route::post('/chat/completions', ChatCompletionController::class)->name('chat.completions');

    // 支付方式 API 路由
    Route::get('/payment-methods', [PaymentMethodController::class, 'index']);
    Route::get('/payment-methods/{identifier}', [PaymentMethodController::class, 'show']);

    // IP池 API 路由
    Route::get('/ip-pools', [IpPoolController::class, 'index']);

    // 工作空间 API 路由
    Route::apiResource('workspaces', WorkspaceController::class);
    Route::post('/workspaces/{workspace}/set-current', [WorkspaceController::class, 'setCurrent'])->name('workspaces.set-current');
    Route::post('/workspaces/{workspace}/retry-namespace', [WorkspaceController::class, 'retryNamespace'])->name('workspaces.retry-namespace');

    // 获取当前工作空间的资源
    Route::get('/workspaces/current/events', [WorkspaceController::class, 'getWorkspaceEvents']);
    Route::get('/workspaces/current/metrics', [WorkspaceController::class, 'getWorkspaceMetrics']);
    Route::get('/workspaces/current/all', [WorkspaceController::class, 'all']);

    // 需要认证的集群 API 路由
    Route::get('/clusters/{cluster}/nodes', [ClusterController::class, 'getNodes']);
    Route::get('/clusters/{cluster}/ingress-external-ips', [ClusterController::class, 'getIngressExternalIps']);

    // 需要工作空间上下文的 API 路由
    Route::middleware([MustInWorkspace::class, WorkspaceMustActive::class])->group(function () {
        // Tasks
        Route::get('/tasks', [TaskController::class, 'index']);
        Route::get('/tasks-stats', [TaskController::class, 'stats']);
        Route::get('/tasks/{task}', [TaskController::class, 'show']);
        Route::post('/tasks/{task}/retry', [TaskController::class, 'retry']);
        Route::post('/tasks/{task}/cancel', [TaskController::class, 'cancel']);

        // Secret API 路由
        Route::apiResource('secrets', SecretController::class)->except(['store', 'create', 'edit']);
        Route::get('/secrets/{name}/data', [SecretController::class, 'data']);

        // 不同类型的 Secret 创建路由
        Route::post('/secrets/generic', [SecretController::class, 'storeGeneric']);
        Route::post('/secrets/docker-registry', [SecretController::class, 'storeDockerRegistry']);
        Route::post('/secrets/tls', [SecretController::class, 'storeTls']);
        Route::post('/secrets/basic-auth', [SecretController::class, 'storeBasicAuth']);
        Route::post('/secrets/ssh-auth', [SecretController::class, 'storeSshAuth']);

        // ConfigMap API 路由
        Route::apiResource('configmaps', ConfigMapController::class)->except(['create', 'edit']);
        Route::post('/configmaps/from-files', [ConfigMapController::class, 'storeFromFiles']);

        // Storage API 路由
        Route::apiResource('storages', StorageController::class)->except(['update', 'create', 'edit']);
        Route::patch('/storages/{name}/expand', [StorageController::class, 'expand']);

        // Deployment API 路由
        Route::apiResource('deployments', DeploymentController::class)->except(['create', 'edit']);
        Route::patch('/deployments/{name}/scale', [DeploymentController::class, 'scale']);

        // StatefulSet API 路由
        Route::apiResource('statefulsets', StatefulSetController::class)->except(['create', 'edit']);
        Route::patch('/statefulsets/{name}/scale', [StatefulSetController::class, 'scale']);

        // Service API 路由
        Route::apiResource('services', ServiceController::class)->except(['create', 'edit']);

        // Ingress API 路由
        Route::apiResource('ingresses', IngressController::class)->except(['create', 'edit']);
        Route::get('/ingresses-classes', [IngressController::class, 'ingressClasses']);
        Route::post('/ingresses/check-domains', [IngressController::class, 'checkDomains']);

        // Pod API 路由
        Route::apiResource('pods', PodController::class)->only(['index', 'show', 'destroy']);
        Route::get('/pods/{name}/logs', [PodController::class, 'logs']);
        Route::post('/pods/{name}/restart', [PodController::class, 'restart']);
        Route::post('/pods/{name}/exec', [PodController::class, 'exec']);

        // Events API 路由
        Route::get('/events', [EventController::class, 'index']);
        Route::get('/resource-events', [EventController::class, 'resourceEvents']);

        // Metrics API 路由
        Route::get('/metrics', [MetricsController::class, 'index']);
        Route::get('/pods/{name}/metrics', [MetricsController::class, 'podMetrics']);

        // Pod Terminal API 路由
        Route::post('/pods/{podName}/terminal/token', [PodTerminalController::class, 'generateToken']);
        Route::get('/pods/{podName}/containers', [PodTerminalController::class, 'getContainers']);

        // HorizontalPodAutoscaler API 路由
        Route::apiResource('hpas', HorizontalPodAutoscalerController::class)->except(['create', 'edit']);
        Route::get('/hpas-scalable-workloads', [HorizontalPodAutoscalerController::class, 'scalableWorkloads']);

        // Webhook API 路由
        Route::get('/webhook-endpoints/options', [WebhookEndpointController::class, 'options']);
        Route::apiResource('webhook-endpoints', WebhookEndpointController::class);
        Route::post('/webhook-endpoints/{webhookEndpoint}/test', [WebhookEndpointController::class, 'test']);
        Route::get('/webhook-endpoints/{webhookEndpoint}/deliveries', [WebhookEndpointController::class, 'deliveries']);
        Route::post('/webhook-endpoints/{webhookEndpoint}/deliveries/{delivery}/resend', [WebhookEndpointController::class, 'resend']);
    });
});
