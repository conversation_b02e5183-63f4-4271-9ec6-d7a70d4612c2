<?php

return [
    'pay' => [
        // 地址
        'url' => env('PAY_URL', ''),
        // 商户 ID
        'mch_id' => env('PAY_MCH_ID', ''),
        // 商户密钥
        'mch_key' => env('PAY_MCH_KEY', ''),
    ],
    'real_name' => [
        'code' => env('REAL_NAME_APP_CODE'),
        'min_age' => env('REAL_NAME_MIN_AGE', 1),
        'max_age' => env('REAL_NAME_MAX_AGE', 80),
        'price' => env('REAL_NAME_PRICE', 0),
    ],
    'face' => [
        'api' => env('DEEPFACE_API', ''),
        'dimension' => 512,
    ],
    'sms' => [
        // 验证码有效时间
        'interval' => env('SMS_INTERVAL', 60),
        'app_key' => env('SMS_APP_KEY', ''),
        'app_secret' => env('SMS_APP_SECRET', ''),
        //            https://www.guoyangyun.com/
        'sign' => env('SMS_SIGN_ID', ''), // 短信签名
        'templates' => [ // 短信模板
            'verify_code' => env('SMS_TEMPLATE_VERIFY_CODE', ''),
        ],
    ],
];
